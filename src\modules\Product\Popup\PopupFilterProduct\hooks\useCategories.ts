import {useState, useCallback} from 'react';
import {categoryAction} from '../../../../../redux/actions/categoryAction';
import {Category} from '../../../../../redux/models/category';

export const useCategories = (parentCategory?: string) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      const config: any = {
        page: 1,
        size: 1000,
      };

      // Nếu có parentCategory, chỉ fetch các category con
      if (parentCategory) {
        config.searchRaw = `@ParentId:{${parentCategory}}`;
      }

      const res = await categoryAction.find(config);
      setCategories(res);
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  }, [parentCategory]);

  return {
    categories,
    loading,
    fetchCategories,
  };
};
