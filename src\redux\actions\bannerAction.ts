import {DataController} from '../../base/baseController';
import {getImage} from './rootAction';

export const bannerAction = {
  find: async (config: {
    page?: number;
    size?: number;
    sortby?: any;
    searchRaw?: string;
  }) => {
    const controller = new DataController('BannerMobile');
    const res = await controller.aggregateList(config);
    if (res?.code === 200) {
      let newData = await getImage({items: res.data});
      return newData;
    }
    return [];
  },
};
