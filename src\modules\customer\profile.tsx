import React, {useRef} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  ComponentStatus,
  FDialog,
  ListTile,
  showDialog,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {navigate, RootScreen} from '../../router/router';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {Ultis} from '../../utils/Utils';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from '@d11/react-native-fast-image';
import iconSvg from '../../svg/icon';
import {StatusOrder} from '../../Config/Contanst';
import MenuOrders from './setting/menuOrders';
import {useEffect, useMemo, useState} from 'react';
import {OrderActions} from '../../redux/reducers/OrderReducer';
import {DataController} from '../../base/baseController';
import {getRankCustomer} from '../../redux/actions/customerAction';

const actionList = [
  {
    id: 0,
    name: 'Tài khoản',
    icon: 'fill/users/profile',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.SettingProfile,
  },

  {
    id: 3,
    name: 'Thiết lập sinh trắc học',
    icon: 'fill/technology/face-recognition',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.BiometricSetting,
  },
  {
    id: 4,
    name: 'Xác thực 2 lớp',
    icon: 'fill/technology/lock-portrait',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.TwoFactorAuth,
  },
  {
    id: 16,
    name: 'Xác minh tài khoản',
    icon: 'fill/user interface/verified',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.AccountAuth,
  },
  {
    id: 6,
    name: 'Sản phẩm yêu thích',
    icon: 'outline/user interface/favorite',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.FavoriteProduct,
  },
  {
    id: 8,
    name: 'Thông tin nhận hàng',
    icon: 'fill/location/map-marker',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.MyAddress,
  },
  {
    id: 7,
    name: 'FAQ',
    show: true,
    icon: 'fill/layout/circle-question',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.FAQView,
  },
  {
    id: 1,
    name: 'Chính sách',
    show: true,
    icon: 'fill/shopping/list',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.PolicyView,
  },
  {
    id: 9,
    name: 'Đăng xuất',
    show: true,
    action: 'logout',
    icon: 'outline/arrows/logout',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.login,
  },
];

export default function Profile({select}: any) {
  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;
  const shopInfo = useSelectorShopState().data;
  const [currentRank, setCurrentRank] = useState(0);
  const [currentBalance, setCurrentBalance] = useState(0);
  const [ranksData, setRanksData] = useState<any[]>([]);
  const orderController = new DataController('Order');
  const [orderDetail, setOrderDetail] = useState<any>([]);
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);

  const {rankInfo} = useSelectorCustomerState();
  // loading
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (customer?.Id) {
      dispatch(getRankCustomer({Id: customer.Id}));
    }
  }, [customer?.Id]);

  useEffect(() => {
    const getPoint = async () => {
      if (customer && rankInfo) {
        if (!customer?.WalletAddress) return;
        const balance = await CustomerActions.getBalance(
          customer.WalletAddress,
        );
        let scoreCurrentRank = 0;
        if (rankInfo?.achievedRank) {
          scoreCurrentRank =
            rankInfo.totalReward >= rankInfo?.achievedRank?.Score || 0
              ? rankInfo.achievedRank?.Score
              : rankInfo.totalReward;
        }
        const currentRank = scoreCurrentRank || 0 + balance || 0;
        const ranksData = rankInfo?.RanksData || [];
        setCurrentRank(currentRank);
        setCurrentBalance(balance);
        setRanksData(ranksData);
      }
    };
    getPoint();
  }, [customer, rankInfo]);

  //navigation
  useEffect(() => {
    if (select == 'Cá nhân') {
      // call order
      getOrderDetail();
    }
  }, [select]);

  useEffect(() => {
    if (shopInfo && shopInfo[0]?.Id) {
      dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
    }
  }, [orderDetail]);

  useEffect(() => {
    //focus effect
    const unsubscribe = navigation.addListener('focus', async () => {
      getOrderDetail();
    });
    return unsubscribe;
  }, [navigation]);

  const getOrderDetail = async () => {
    if (!customer?.Id) return;
    setLoading(true);
    const response = await orderController.getPatternList({
      query: `@CustomerId: {${customer?.Id}}`,
      pattern: {
        ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
      },
    });
    if (response?.code === 200) {
      setOrderDetail(response.data);
      setLoading(false);

      return response;
    }
    setLoading(false);
    return null;
  };

  const rankProgress = useMemo(() => {
    if (ranksData.length === 0) {
      return {
        nextPoints: 0,
        currentRankInfo: null,
        nextRankInfo: null,
      };
    }

    // Sắp xếp ranks theo tổng điểm (Score + Token) tăng dần
    const sortedRanks = [...ranksData].sort(
      (a, b) =>
        parseFloat(a.Score) +
        parseFloat(a.Token || 0) -
        (parseFloat(b.Score) + parseFloat(b.Token || 0)),
    );

    // Tính tổng điểm hiện tại của user (Score + Token)
    const currentTotalPoints = currentRank;

    // Tìm hạng hiện tại và hạng tiếp theo
    let currentRankInfo = null;
    let nextRankInfo = null;

    // Tìm hạng hiện tại (hạng cao nhất mà user đã đạt được)
    for (let i = sortedRanks.length - 1; i >= 0; i--) {
      const rankTotalPoints =
        parseFloat(sortedRanks[i].Score) +
        parseFloat(sortedRanks[i].Token || 0);
      if (currentTotalPoints >= rankTotalPoints) {
        currentRankInfo = sortedRanks[i];
        // Tìm hạng tiếp theo (nếu có)
        if (i < sortedRanks.length - 1) {
          nextRankInfo = sortedRanks[i + 1];
        } else {
          // Đã đạt hạng cao nhất
          nextRankInfo = currentRankInfo;
        }
        break;
      }
    }

    // Nếu chưa đạt hạng nào, hạng tiếp theo là hạng thấp nhất
    if (!currentRankInfo && sortedRanks.length > 0) {
      nextRankInfo = sortedRanks[0];
    }

    let nextPoints = 0;
    if (nextRankInfo && currentRankInfo?.Id !== nextRankInfo?.Id) {
      const currentRankTotalPoints = currentRankInfo
        ? parseFloat(currentRankInfo.Score)
        : 0;
      const currentRankTotalBalance = currentRankInfo
        ? parseFloat(currentRankInfo.Token || 0)
        : 0;

      const nextRankTotalPoints = parseFloat(nextRankInfo.Score || 0);
      const nextRankTotalBalance = nextRankInfo
        ? parseFloat(nextRankInfo.Token || 0)
        : 0;

      // kiểm tra xem currentRank có lớn hơn nextRankTotalPoints không
      if (
        currentRankTotalPoints >= nextRankTotalPoints ||
        (rankInfo && rankInfo.totalReward >= nextRankTotalPoints)
      ) {
        // nếu rankInfo.totalReward >= nextRankTotalPoints thì nextPoint tính theo token còn thiếu
        nextPoints = nextRankTotalBalance - currentRankTotalBalance;
      } else if (currentRankTotalBalance >= nextRankTotalBalance) {
        nextPoints = nextRankTotalPoints - currentRankTotalPoints;
      } else {
        nextPoints =
          nextRankTotalPoints -
          currentRankTotalPoints +
          (nextRankTotalBalance - currentRankTotalBalance);
      }
    } else {
      // Đã đạt hạng cao nhất
      nextPoints = 0;
    }

    return {
      nextPoints,
      currentRankInfo,
      nextRankInfo,
    };
  }, [ranksData, currentRank, customer?.Token]);

  const {nextPoints, currentRankInfo, nextRankInfo} = rankProgress;

  const pointToNext = useMemo(() => {
    if (nextPoints > 0) {
      // neu point đủ và thiếu token
      if (rankInfo && rankInfo.totalReward >= nextRankInfo?.Score) {
        return `Cần ${Ultis.money(nextPoints)} điểm TOKEN để lên hạng`;
      }
      // nếu point thiếu và đủ token
      if (currentRank + currentBalance >= nextRankInfo?.Token) {
        return `Cần ${Ultis.money(nextPoints)} điểm POINT để lên hạng`;
      }
      // nếu thiếu cả point và token
      return `Cần ${Ultis.money(nextPoints)} điểm để lên hạng`;
    } else {
      return 'Đã đạt hạng cao nhất';
    }
  }, [nextPoints, rankInfo]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FDialog ref={dialogRef} />
      {customer || loading ? (
        <TouchableOpacity
          onPress={() => {
            navigate(RootScreen.ProfileRankScreen);
          }}
          style={styles.progressSection}>
          <View
            style={{
              ...(currentRankInfo?.Id == nextRankInfo?.Id && {
                justifyContent: 'center',
                alignContent: 'center',
                alignItems: 'center',
              }),
            }}>
            {currentRankInfo?.Id == nextRankInfo?.Id && (
              <FastImage
                source={{
                  uri: ConfigAPI.urlImg + currentRankInfo?.Icon,
                }}
                style={{width: 45, height: 45}}
                resizeMode="contain"
              />
            )}
            <Text style={styles.progressInfoValue}>
              {loading ? '...' : currentRankInfo?.Name ?? 'Chưa có hạng'}
            </Text>
            {nextRankInfo ? (
              <Text style={styles.pointsToNext}>{pointToNext}</Text>
            ) : null}
          </View>

          {/* progress */}
          {currentRankInfo?.Id === nextRankInfo?.Id ? null : (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 4,
              }}>
              {currentRankInfo?.Icon ? (
                <FastImage
                  source={{
                    uri: ConfigAPI.urlImg + currentRankInfo?.Icon,
                  }}
                  style={{width: 30, height: 30}}
                  resizeMode="contain"
                />
              ) : (
                <FastImage
                  source={require('../../assets/images/logo.png')}
                  style={{width: 30, height: 30}}
                  resizeMode="contain"
                />
              )}
              {/* progress */}
              <View style={styles.progressBarBackground}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.secondary1_main_color,
                  }}>
                  {Ultis.money(currentRank + currentBalance)}/
                  {Ultis.money(
                    (nextRankInfo?.Score || 0) + (nextRankInfo?.Token || 0),
                  )}
                </Text>
              </View>
              {nextRankInfo?.Icon ? (
                <FastImage
                  source={{
                    uri: ConfigAPI.urlImg + nextRankInfo?.Icon,
                  }}
                  style={{width: 30, height: 30}}
                  resizeMode="contain"
                />
              ) : null}
            </View>
          )}
        </TouchableOpacity>
      ) : null}
      {customer ? (
        <View
          style={{
            width: '100%',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-around',
            paddingHorizontal: 8,
            marginBottom: 8,
          }}>
          <MenuOrders
            svgIcon={iconSvg.walletAction}
            title="Đơn hàng mới"
            getBadgeOrder={
              orderDetail?.filter((item: any) => item.Status == StatusOrder.new)
                .length
            }
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.new}
          />
          <MenuOrders
            svgIcon={iconSvg.deliveryIcon}
            title="Đang xử lý"
            getBadgeOrder={
              orderDetail?.filter(
                (item: any) => item.Status == StatusOrder.proccess,
              ).length
            }
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.proccess}
          />
          <MenuOrders
            svgIcon={iconSvg.done}
            title="Hoàn thành"
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.success}
          />
          <MenuOrders
            svgIcon={iconSvg.cancel}
            title="Hủy/hoàn"
            getBadgeOrder={
              orderDetail?.filter(
                (item: any) => item.Status == StatusOrder.cancel,
              ).length
            }
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.cancel}
          />
          <MenuOrders
            svgIcon={iconSvg.star}
            title="Đánh giá"
            orderRoute={RootScreen.RatingForAllScreen}
          />
        </View>
      ) : null}
      {(!customer ? actionList.filter(item => item.show) : actionList).map(
        item => (
          <ListTile
            key={item.id}
            style={{
              padding: 0,
            }}
            listtileStyle={{
              paddingRight: 16,
              paddingVertical: 13,
              gap: 8,
              borderBottomColor: ColorThemes.light.primary_background,
              borderBottomWidth: 1,
              marginLeft: 16,
            }}
            onPress={() => {
              if (item.action === 'logout') {
                // Handle logout action here
                console.log('Logout action triggered');
                // navigateReset(RootScreen.login);
                if (!customer) {
                  dispatch(CustomerActions.logout());
                  return;
                }
                showDialog({
                  ref: dialogRef,
                  status: ComponentStatus.WARNING,
                  title: 'Bạn chắc chắn muốn đăng xuất?',
                  onSubmit: async () => {
                    dispatch(CustomerActions.logout());
                  },
                });
                return;
              }

              if (item.route) navigate(item.route);
            }}
            leading={
              <View
                style={{
                  height: 32,
                  width: 32,
                  borderRadius: 4,
                  padding: 6,
                  backgroundColor: item.background,
                }}>
                <Winicon src={item.icon} color={item.colorIcon} size={20} />
              </View>
            }
            title={
              !customer && item.action === 'logout' ? 'Về đăng nhập' : item.name
            }
            titleStyle={[
              TypoSkin.heading8,
              {color: ColorThemes.light.neutral_text_title_color},
            ]}
            trailing={
              <Winicon
                src="outline/arrows/right-arrow"
                color={ColorThemes.light.neutral_text_subtitle_color}
                size={16}
              />
            }
          />
        ),
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  statusContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.success_main_color,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    gap: 6,
  },
  statusText: {
    ...TypoSkin.label3,
    color: ColorThemes.light.white,
    fontSize: 12,
  },
  timestampText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  detailsContainer: {
    borderRadius: 16,
    padding: 16,
    gap: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  detailLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 1,
    fontSize: 14,
    fontWeight: '600',
  },
  detailValue: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 2,
    textAlign: 'right',
    fontSize: 14,
    fontWeight: '600',
  },
  recipientInfo: {
    flex: 2,
    alignItems: 'flex-end',
  },
  recipientPhone: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    // marginTop: 2,
    fontSize: 12,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: ColorThemes.light.neutral_lighter_border_color,
    marginVertical: 8,
    //dotte border
    borderStyle: 'dashed',
    borderWidth: 1,
    borderBottomColor: '#000',
  },
  amountValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.primary_main_color,
    flex: 2,
    textAlign: 'right',
    fontSize: 14,
    fontWeight: '600',
  },
  transactionIdContainer: {
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionIdLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  transactionIdRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 8,
  },
  transactionIdValue: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontFamily: 'monospace',
    fontWeight: '600',
  },

  progressSection: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    margin: 16,
    borderRadius: 20,
    padding: 16,
    gap: 8,
    borderColor: ColorThemes.light.primary_border_color,
    borderWidth: 1,
  },
  currentRankProgress: {
    marginBottom: 0,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  progressPercentage: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.primary_main_color,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 24,
    backgroundColor: ColorThemes.light.secondary1_background,
    borderRadius: 50,
    overflow: 'hidden',
    flex: 1,
    alignItems: 'center',
    alignContent: 'center',
    alignSelf: 'center',
    textAlign: 'center',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  progressInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  progressInfoLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  progressInfoValue: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  currentPointsDisplay: {
    backgroundColor: ColorThemes.light.secondary3_main_color,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  currentPointsLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  currentPointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  pointsToNext: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});
