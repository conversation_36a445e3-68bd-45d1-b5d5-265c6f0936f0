import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {Brand} from '../models/brand';

const brandAction = {
  fetch: async (config: any) => {
    const controller = new DataController('Brand');
    const params: any = {
      page: config?.page ?? 1,
      size: config?.size ?? 1000,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    };
    if (config?.searchRaw) params.searchRaw = config.searchRaw;
    if (config?.sortby) params.sortby = config.sortby;
    const res = await controller.aggregateList(params);
    if (res?.code === 200) {
      return res.data;
    }
    return [];
  },
  findOne: async (id: string) => {
    const controller = new DataController('Brand');
    const res = await controller.getById(id);
    return res.data;
  },
};

const fetchBrands = createAsyncThunk<
  Brand[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('brand/fetchBrands', async (config, thunkAPI: any) => {
  const controller = new DataController('Brand');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 1000,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res?.code === 200) {
      return res.data;
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchBrands, brandAction};
