import React, {useState, useRef} from 'react';
import {
  View,
  TouchableOpacity,
  Dimensions,
  Animated,
  StyleSheet,
  Text,
} from 'react-native';
import FastImage from '@d11/react-native-fast-image';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {navigate, RootScreen} from '../../../router/router';
import type {BannerItem} from '../../../redux/models/banner';

interface PaginationDotsProps {
  activeIndex: number;
  totalItems: number;
}
interface BannerItemProps {
  item: BannerItem;
  fadeAnim: Animated.Value;
}
interface BannerSectionProps {
  bannerData?: BannerItem[];
  loading?: boolean;
}

// ========================= CONSTANTS =========================
const SCREEN_WIDTH = Dimensions.get('window').width;
const BANNER_HEIGHT = 176;
const AUTOPLAY_DELAY = 3;
const ANIMATION_DURATION = 300;

// ========================= CUSTOM HOOKS =========================
const useBannerAnimation = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const [isAutoplayEnabled, setIsAutoplayEnabled] = useState(true);

  const fadeIn = () => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: ANIMATION_DURATION,
      useNativeDriver: true,
    }).start();
  };

  const handleIndexChange = (index: number) => {
    setActiveIndex(index);
    fadeIn();
  };

  const handleUserInteraction = () => {
    setIsAutoplayEnabled(false);
    setTimeout(() => {
      setIsAutoplayEnabled(true);
    }, 5000);
  };

  return {
    activeIndex,
    fadeAnim,
    isAutoplayEnabled,
    handleIndexChange,
    handleUserInteraction,
  };
};

const BannerItem: React.FC<BannerItemProps> = ({item}: {item: BannerItem}) => {
  return (
    <View key={item.Id} style={styles.bannerItemContainer}>
      <FastImage
        source={{uri: item.Img}}
        style={styles.bannerImage}
        resizeMode={FastImage.resizeMode.cover}
      />
    </View>
  );
};

const PaginationDots: React.FC<PaginationDotsProps> = ({
  activeIndex,
  totalItems,
}) => (
  <View style={styles.paginationContainer}>
    {Array.from({length: totalItems}).map((_, index) => (
      <View
        key={index}
        style={[
          styles.paginationDot,
          index === activeIndex && styles.paginationDotActive,
        ]}
      />
    ))}
  </View>
);

const BannerSection: React.FC<BannerSectionProps> = ({
  bannerData = [],
  loading = false,
}) => {
  const {
    activeIndex,
    fadeAnim,
    isAutoplayEnabled,
    handleIndexChange,
    handleUserInteraction,
  } = useBannerAnimation();

  const handleBannerPress = () => {
    navigate(RootScreen.GiftExchange);
  };

  const renderBannerItem = ({item}: {item: BannerItem}) => (
    <BannerItem item={item} fadeAnim={fadeAnim} />
  );

  const handleSwipeStart = () => {
    handleUserInteraction();
  };

  // Show loading state
  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </View>
    );
  }

  // Show empty state if no data
  if (!bannerData || bannerData.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No banners available</Text>
        </View>
      </View>
    );
  }

  return (
    <TouchableOpacity onPress={handleBannerPress} style={styles.container}>
      <SwiperFlatList
        autoplay={isAutoplayEnabled}
        autoplayDelay={AUTOPLAY_DELAY}
        autoplayLoop
        showPagination={false}
        pagingEnabled
        horizontal
        disableGesture={false}
        style={styles.swiperStyle}
        data={bannerData}
        onChangeIndex={({index}: {index: number}) => handleIndexChange(index)}
        onMomentumScrollBegin={handleSwipeStart}
        renderItem={renderBannerItem}
        keyExtractor={(item, index) => `banner-${index}`}
        removeClippedSubviews={true}
        initialNumToRender={1}
        maxToRenderPerBatch={1}
        windowSize={3}
        getItemLayout={(data, index) => ({
          length: SCREEN_WIDTH,
          offset: SCREEN_WIDTH * index,
          index,
        })}
      />

      <PaginationDots
        activeIndex={activeIndex}
        totalItems={bannerData.length}
      />
    </TouchableOpacity>
  );
};

// ========================= STYLES =========================
const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    height: BANNER_HEIGHT,
    overflow: 'hidden',
  },
  actionButtonContainer: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    zIndex: 99,
  },
  actionButtonStyle: {
    justifyContent: 'flex-start',
    alignSelf: 'baseline',
  },
  swiperStyle: {
    width: SCREEN_WIDTH,
    height: '100%',
  },
  bannerItemContainer: {
    width: SCREEN_WIDTH,
    height: '100%',
  },
  bannerTextContainer: {
    position: 'absolute',
    bottom: 60,
    left: 16,
    zIndex: 99,
  },
  bannerText: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_absolute_background_color,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 3,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f2f2f2',
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 8,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 98,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: 20,
    borderRadius: 4,
  },
  counterContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 98,
  },
  counterText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f2f2f2',
  },
  loadingText: {
    ...TypoSkin.subtitle2,
    color: ColorThemes.light.neutral_300,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f2f2f2',
  },
  emptyText: {
    ...TypoSkin.subtitle2,
    color: ColorThemes.light.neutral_300,
  },
});

export default BannerSection;
