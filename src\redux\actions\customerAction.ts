import {createAsyncThunk} from '@reduxjs/toolkit';
import {DataController} from '../../base/baseController';
import { TransactionStatus, TransactionType } from '../../Config/Contanst';
import store from '../store/store';
import { CustomerActions } from '../reducers/CustomerReducer';

const getRankCustomer = createAsyncThunk(
  'customer/getRankCustomer',
  async (
    {
      Id,
    }: {Id: string},
    {rejectWithValue},
  ) => {
    try {
      let totalReward = 0;
      //current customer 
      const customer = store.getState().customer.data;
      // Gọi tất cả API cùng lúc với Promise.all
      const [totalRewardRes, resSum, resRank, balance] = await Promise.all([
        // 1. Tính tổng điểm tất cả
        new DataController('HistoryReward').group({
          reducers:
            'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
          searchRaw: `@CustomerId: {${Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
        }),

        // 2. Tính tổng điểm từ hoa hồng và nhiệm vụ
        new DataController('HistoryReward').group({
          reducers:
            'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
          searchRaw: `@CustomerId: {${Id}} (@Type: [${TransactionType.hoahong}] | @Type: [${TransactionType.mission}]) @Status: [${TransactionStatus.success}]`,
        }),

        // 3. Lấy thông tin config rank
        new DataController('ConfigRank').getAll(),
        // 4. Lấy số dư ví
       await CustomerActions.getBalance(customer.WalletAddress)
      ]);

      // Xử lý kết quả tổng điểm hiển thị
      if (totalRewardRes?.code === 200 && totalRewardRes.data.length > 0) {
        totalReward = parseFloat(totalRewardRes.data[0].TotalReward || 0);
      }

      // Xử lý kết quả điểm để tính rank
      let totalScore = 0;
      if (resSum.code === 200 && resSum.data.length > 0) {
        totalScore = parseFloat(resSum.data[0].TotalReward || 0);
      }

      // Xử lý rank và xác định rank hiện tại
      if (resRank.code === 200 && resRank.data.length > 0) {
        const ranksData = resRank.data;

        // Sắp xếp ranks theo điểm số tăng dần
        const sortedRanks = [...ranksData].sort(
          (a, b) => parseFloat(a.Score) - parseFloat(b.Score),
        );

        // Tìm rank hiện tại dựa trên điều kiện
        let achievedRank = null;

        for (const rank of sortedRanks) {
          const requiredScore = parseFloat(rank.Score);
          const token = parseFloat(rank.Token);
          // Kiểm tra điều kiện điểm số
          if (totalScore >= requiredScore && token <= balance) {
            achievedRank = rank;
          }
        }
        return {
          totalReward,
          totalScore,
          achievedRank,
          RanksData: sortedRanks,
          balance
        };
      } else {
        return {
          totalReward,
          totalScore,
          achievedRank: null,
          RanksData: [],
          balance: 0
        };
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      return rejectWithValue(error);
    }
  },
);

export {getRankCustomer};
