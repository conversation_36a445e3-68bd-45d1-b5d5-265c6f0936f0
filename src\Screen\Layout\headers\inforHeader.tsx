import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {
  StyleSheet,
  View,
  Image,
  Text,
  TouchableOpacity,
  ViewStyle,
  GestureResponderEvent,
  TextStyle,
} from 'react-native';
import {Divider} from 'react-native-paper';
import {Winicon} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';
import {ColorThemes} from '../../../assets/skin/colors';
import HeaderBackground from '../../../modules/shop/component/HeaderShop';
import {RootScreen} from '../../../router/router';

interface ScreenHeaderProps {
  title: string;
  customActions?: React.ReactNode;
  showAction?: boolean;
  showBack?: boolean;
  showDivider?: boolean;
  showSearch?: boolean;
  onBack?: () => void;
  onPressQrcode?: () => void;
}

export const InforHeader = ({
  title,
  showAction = false,
  showDivider = true,
  showSearch = false,
  onBack,
  showBack = true,
  onPressQrcode,
  customActions,
}: ScreenHeaderProps) => {
  const navigation = useNavigation<any>();

  const handleBack = () => {
    if (onBack) return onBack();
    navigation.goBack();
  };

  const handleNotificationPress = () => {
    navigation.navigate(RootScreen.Notification);
  };

  return (
    <View
      style={{
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <HeaderBackground />
      <View style={styles.navigator}>
        {showBack ? (
          <TouchableOpacity style={styles.back} onPress={handleBack}>
            <Winicon
              src="outline/arrows/left-arrow"
              size={14}
              style={styles.navigatorIcon}
            />
            <Image
              source={require('../../../assets/images/logo.png')}
              style={styles.navigatorImage}
            />
          </TouchableOpacity>
        ) : (
          <View style={{minWidth: 60}} />
        )}
        <View style={styles.titleNavigatorContainer}>
          <Text style={styles.titleNavigator}>{title}</Text>
        </View>
        {showAction ? (
          customActions ? (
            <View style={styles.actions}>{customActions}</View>
          ) : (
            <View style={styles.actions}>
              {onPressQrcode ? (
                <TouchableOpacity onPress={onPressQrcode}>
                  <Image
                    source={require('../../../assets/images/icon_navigator.png')}
                    style={styles.navigatorImage}
                  />
                </TouchableOpacity>
              ) : null}
              <TouchableOpacity onPress={handleNotificationPress}>
                <Image
                  source={require('../../../assets/images/icon_bell_navigator.png')}
                  style={styles.navigatorImage}
                />
              </TouchableOpacity>
            </View>
          )
        ) : (
          <View style={styles.actions} />
        )}
      </View>
      {showDivider && <Divider />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: ColorThemes.light.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    maxHeight: 120,
  },
  title: {
    position: 'absolute',
    left: '12%',
    right: '12%',

    paddingBottom: 8,
  },
  navigator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 18,
    width: '100%',
  },
  navigatorImage: {
    width: 32,
    height: 32,
    borderRadius: 50,
  },
  navigatorIcon: {
    marginTop: 4,
  },
  titleNavigatorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleNavigator: {
    fontSize: 16,
    fontFamily: 'roboto',
    marginLeft: 20,
  },
  back: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginLeft: 16,
  },
  backContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actions: {
    minWidth: 60,
    flexDirection: 'row',
    marginRight: 15,
    gap: 2,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
});
