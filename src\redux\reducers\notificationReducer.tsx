import {PayloadAction, createSlice, Dispatch} from '@reduxjs/toolkit';
import notifee from '@notifee/react-native';

import {NotificationItem} from '../models/notification';
import {
  fetchNotifications,
  readAllNotification,
} from '../actions/notificationAction';
import {DataController} from '../../base/baseController';
import store from '../store/store';
import {CustomerActions} from './CustomerReducer';

interface notificationSimpleResponse {
  data: Array<NotificationItem>;
  totalCount: number;
  loading: boolean;
  badgeCount: number;
  onLoading: boolean;
}

export type {notificationSimpleResponse};

const initState: notificationSimpleResponse = {
  data: [],
  totalCount: 0,
  loading: false,
  badgeCount: 0,
  onLoading: false,
};

export const notificationSlice = createSlice({
  name: 'notification',
  initialState: initState,
  reducers: {
    setData: <K extends keyof notificationSimpleResponse>(
      state: notificationSimpleResponse,
      action: PayloadAction<{
        stateName: K;
        data: notificationSimpleResponse[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETDATA':
          state.data = action.payload.data;
          state.totalCount = action.payload.totalCount || 0;
          break;
        case 'SETBADGE':
          state.badgeCount = action.payload.badgeCount;
          break;
        case 'RESET':
          state.data = [];
          state.totalCount = 0;
          state.badgeCount = 0;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchNotifications.pending, state => {
      state.loading = true;
    });
    builder.addCase(fetchNotifications.fulfilled, (state, action) => {
      state.data = action.payload;
      state.loading = false;
    });
    builder.addCase(readAllNotification.pending, state => {
      state.loading = true;
    });
    builder.addCase(readAllNotification.fulfilled, state => {
      state.loading = false;
    });
  },
});

const {setData, handleActions, onFetching} = notificationSlice.actions;

export default notificationSlice.reducer;

export class NotificationActions {
  static getData =
    (dispatch: Dispatch, config?: {page?: number; status?: number}) =>
    async () => {
      dispatch(onFetching());
      const cusId = store.getState().customer.data?.Id;
      if (!cusId) return;

      try {
        const controller = new DataController('Notification');
        const searchParams = {
          page: config?.page ?? 1,
          size: 10,
          searchRaw: `@CustomerId:{${cusId}}${
            config?.status !== undefined ? ` @Status:[${config.status}]` : ''
          }`,
          sortby: [{prop: 'DateCreated', direction: 'DESC' as const}],
        };

        const res = await controller.aggregateList(searchParams);

        if (res?.code === 200) {
          dispatch(
            handleActions({
              type: 'GETDATA',
              data: res.data || [],
              totalCount: res.totalCount || 0,
            }),
          );
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
        dispatch(
          handleActions({
            type: 'GETDATA',
            data: [],
            totalCount: 0,
          }),
        );
      }
    };

  static setBadge = async (dispatch: Dispatch) => {
    try {
      const badgeCount = await notifee.getBadgeCount();
      dispatch(
        handleActions({
          type: 'SETBADGE',
          badgeCount: badgeCount,
        }),
      );
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  };

  static incrementBadge = async (dispatch: Dispatch) => {
    try {
      await notifee.incrementBadgeCount();
      const badgeCount = await notifee.getBadgeCount();
      dispatch(
        handleActions({
          type: 'SETBADGE',
          badgeCount: badgeCount,
        }),
      );
      console.log('Badge count incremented to:', badgeCount);
    } catch (error) {
      console.error('Error incrementing badge count:', error);
    }
  };

  static decrementBadge = async (dispatch: Dispatch) => {
    try {
      await notifee.decrementBadgeCount();
      const badgeCount = await notifee.getBadgeCount();
      dispatch(
        handleActions({
          type: 'SETBADGE',
          badgeCount: badgeCount,
        }),
      );
      console.log('Badge count decremented to:', badgeCount);
    } catch (error) {
      console.error('Error decrementing badge count:', error);
    }
  };

  static clearBadge = async (dispatch: Dispatch) => {
    try {
      await notifee.setBadgeCount(0);
      dispatch(
        handleActions({
          type: 'SETBADGE',
          badgeCount: 0,
        }),
      );
      console.log('Badge count cleared');
    } catch (error) {
      console.error('Error clearing badge count:', error);
    }
  };

  static reset = (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: 'RESET',
      }),
    );
  };

  static readAllNotifications = (dispatch: any, type: number[]) => async () => {
    dispatch(onFetching());
    try {
      dispatch(readAllNotification(type));
      // Refresh data after marking all as read
      NotificationActions.getData(dispatch)();
    } catch (error) {
      console.error('Error reading all notifications:', error);
    }
  };

  static markAsRead = async (dispatch: Dispatch, notificationId: string) => {
    try {
      const controller = new DataController('Notification');
      const res = await controller.edit([
        {
          Id: notificationId,
          Status: 1, // Mark as read
        },
      ]);

      if (res?.code === 200) {
        console.log('📖 [Notification] Marked as read:', notificationId);
        // Refresh notifications data
        NotificationActions.getData(dispatch)();
      }
      return res;
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  static deleteNotification = async (
    dispatch: Dispatch,
    notificationId: string,
  ) => {
    try {
      const controller = new DataController('Notification');
      const res = await controller.delete([notificationId]);

      if (res?.code === 200) {
        console.log('🗑️ [Notification] Deleted:', notificationId);
        // Refresh notifications data
        NotificationActions.getData(dispatch)();
      }
      return res;
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  static getUnreadCount = async (): Promise<number> => {
    try {
      const cusId = store.getState().customer.data?.Id;
      if (!cusId) return 0;

      const controller = new DataController('Notification');
      const res = await controller.aggregateList({
        page: 1,
        size: 0, // We only need the count
        searchRaw: `@CustomerId:{${cusId}} @Status:[0]`, // Status 0 = unread
      });

      return res.totalCount || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  };

  static syncBadgeWithUnreadCount = async (dispatch: Dispatch) => {
    try {
      const unreadCount = await NotificationActions.getUnreadCount();
      await notifee.setBadgeCount(unreadCount);
      dispatch(
        handleActions({
          type: 'SETBADGE',
          badgeCount: unreadCount,
        }),
      );
      console.log(
        '🔄 [Notification] Badge synced with unread count:',
        unreadCount,
      );
    } catch (error) {
      console.error('Error syncing badge with unread count:', error);
    }
  };

  // Handle notification click with detailed logging
  static handleNotificationClick = async (
    dispatch: Dispatch,
    notificationData: any,
    context: 'foreground' | 'background' | 'quit',
  ) => {
    console.log(`🔔 [Notification] Click handled in ${context}:`, {
      notificationId: notificationData?.id,
      type: notificationData?.type,
      data: notificationData?.data,
      timestamp: new Date().toISOString(),
    });

    try {
      // Mark notification as read if we have the ID
      if (notificationData?.id) {
        await NotificationActions.markAsRead(dispatch, notificationData.id);
      }

      // Decrement badge count
      await NotificationActions.decrementBadge(dispatch);

      // Update customer info
      store.dispatch(CustomerActions.getInfor());

      // Handle navigation based on notification type
      if (notificationData?.data?.type) {
        switch (notificationData.data.type) {
          case 'chat':
            console.log('💬 [Notification] Navigating to chat');
            // Add chat navigation logic here
            break;
          case 'order':
            console.log('🛒 [Notification] Navigating to orders');
            // Add order navigation logic here
            break;
          case 'general':
          default:
            console.log('📢 [Notification] Navigating to notifications');
            // Navigate to notifications screen
            break;
        }
      }

      // Log successful handling
      console.log('✅ [Notification] Click handled successfully');
    } catch (error) {
      console.error('❌ [Notification] Error handling click:', error);
    }
  };
}

export {setData};
