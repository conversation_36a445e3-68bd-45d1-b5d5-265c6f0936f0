import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Pressable,
  RefreshControl,
} from 'react-native';
import {FlatList, ScrollView} from 'react-native-gesture-handler';
import {
  AppSvg,
  ComponentStatus,
  showSnackbar,
  Winicon,
  FDialog,
  showDialog,
} from 'wini-mobile-components';
import {TypeMenuPorduct} from '../../../Config/Contanst';
import {ManageProductDetailProps} from '../../../components/dto/dto';
import {RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {DataController} from '../../../base/baseController';
import {useDispatch} from 'react-redux';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import {ProductActions} from '../../../redux/reducers/ProductReducer';
import {TypoSkin} from '../../../assets/skin/typography';
import iconSvg from '../../../svg/icon';
import {CustomSwitch, ReverseCustomSwitch} from '../../../components/switch';
import {ColorThemes} from '../../../assets/skin/colors';
import {Ultis} from '../../../utils/Utils';
import FastImage from '@d11/react-native-fast-image';
import {ListManageProductDetailStyles} from '../styles/ListManageProductDetailStyles';
import {ProductDA} from '../productDA';
const ManageProductDetail = (props: ManageProductDetailProps) => {
  const {menu, dataShop} = props;
  let [data, setData] = useState<any[]>([]);
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const shopInfo = useSelectorShopState().data;
  const [isOn, setIsOn] = useState(true);
  const [selectSwitchData, setSelectSwitchData] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);
  const dialogRef = useRef<any>(null);
  const productDA = new ProductDA();
  useEffect(() => {
    if (menu == 'Còn hàng') {
      setData(dataShop[0]?.data);
    } else if (menu == 'Hết hàng') {
      setData(dataShop[1]?.data);
    } else if (menu == 'Chờ duyệt') {
      setData(dataShop[2]?.data);
    } else if (menu == 'Vi phạm') {
      setData(dataShop[3]?.data);
    } else if (menu == 'Ẩn') {
      setData(dataShop[4]?.data);
    }
  }, [menu, dataShop]);
  const handleEditProduct = (data: any) => {
    if (data) {
      navigation.navigate(RootScreen.CreateNewProduct, {
        dataEdit: data,
        title: 'Chỉnh sửa sản phẩm',
      });
    }
  };
  const handleCopyProduct = (data: any) => {
    if (data) {
      navigation.navigate(RootScreen.CreateNewProduct, {
        dataCopy: data,
        title: 'Copy sản phẩm',
      });
    }
  };
  const handleDeleteProduct = async (data: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn xoá sản phẩm này?',
      onSubmit: async () => {
        let respone = await productDA.deleteProduct(data?.Id);
        if (respone.code == 200) {
          dispatch(ProductActions.getInforProduct(shopInfo[0].Id));
          showSnackbar({
            message: 'Xóa sản phẩm thành công',
            status: ComponentStatus.SUCCSESS,
          });
        }
      },
    });
  };
  const handleViewViolation = (data: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Lỗi vi phạm',
      onSubmit: async () => {
        showSnackbar({
          message: data,
          status: ComponentStatus.SUCCSESS,
        });
      },
    });
  };
  const handleChangeStatusSwitch = async (item: any, status: number) => {
    setSelectSwitchData(item?.Id || item?.id || '');
    let EditProduct = {
      Id: item?.Id || item?.item?.Id,
      DateCreated: item?.DateCreated || item?.item?.DateCreated,
      Name: item?.Name || item?.item?.Name,
      Description: item?.Description || item?.item?.Description,
      Img: item?.Img?.split('/').pop() || item?.item?.Img?.split('/').pop(),
      Price: item?.Price || item?.item?.Price,
      Status: status,
      Content: item?.Content || item?.item?.Content,
      ListImg: item?.ListImg || item?.item?.ListImg,
      Instock: item?.Instock || item?.item?.Instock,
      CategoryId: item?.CategoryId || item?.item?.CategoryId,
      BrandId: item?.BrandId || item?.item?.BrandId,
      ShopId: item?.ShopId || item?.item?.ShopId,
    };
    let res = await productDA.updateProduct([EditProduct]);
    if (res?.code == 200) {
      if (status == 5) {
        showSnackbar({
          message: 'Cập nhật trạng thái sản phẩm thành công sang ẩn',
          status: ComponentStatus.SUCCSESS,
        });
        dispatch(ProductActions.getInforProduct(shopInfo[0].Id));
      } else if (status == 1) {
        showSnackbar({
          message: 'Cập nhật trạng thái sản phẩm thành công hoạt động',
          status: ComponentStatus.SUCCSESS,
        });
        dispatch(ProductActions.getInforProduct(shopInfo[0].Id));
      } else if (status == 3) {
      }
    }
  };
  const CustomSwitchComponent = (item: any) => {
    return (
      <CustomSwitch
        isOn={isOn}
        onPress={() => handleChangeStatusSwitch(item, 5)}
      />
    );
  };
  const ReverseCustomSwitchComponent = (item: any) => {
    return (
      <ReverseCustomSwitch
        isOn={isOn}
        onPress={() => {
          if (item?.item?.InStock == 0) {
            showSnackbar({
              message:
                'Vui lòng cập nhật số lượng tồn kho trước khi chuyển trạng thái sản phẩm',
              status: ComponentStatus.WARNING,
            });
          } else {
            handleChangeStatusSwitch(item, 1);
          }
        }}
      />
    );
  };
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await dispatch(ProductActions.getInforProduct(shopInfo[0].Id));
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [dispatch, shopInfo]);

  useEffect(() => {
    if (shopInfo && shopInfo[0]?.Id) {
      handleRefresh();
    }
  }, [menu, handleRefresh]);

  if (data && data.length > 0) {
    return (
      <>
        <FDialog ref={dialogRef} />
        <FlatList
          data={data}
          style={{flex: 1}}
          keyExtractor={item => item.Id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[ColorThemes.light.primary_main_color]}
              tintColor={ColorThemes.light.primary_main_color}
              progressBackgroundColor="#ffffff"
              progressViewOffset={10}
            />
          }
          showsVerticalScrollIndicator={true}
          bounces={true}
          alwaysBounceVertical={true}
          contentContainerStyle={{flexGrow: 1}}
          renderItem={({item, index}) => (
            <Pressable style={ListManageProductDetailStyles.productList}>
              <View style={ListManageProductDetailStyles.productCard}>
                <FastImage
                  source={{uri: item.Img}}
                  style={ListManageProductDetailStyles.productImage}
                />

                <View style={ListManageProductDetailStyles.productInfo}>
                  <Text style={ListManageProductDetailStyles.productName}>
                    {item.Name}
                  </Text>
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <View style={ListManageProductDetailStyles.ratingContainer}>
                      <Text style={ListManageProductDetailStyles.rating}>
                        <View style={{paddingRight: 6}}>
                          <Winicon
                            src="fill/user interface/star"
                            size={15}
                            color="#FFD700"
                          />
                        </View>
                        <Text style={{...TypoSkin.body3}}>
                          {item.Star ? item.Star : 0}
                        </Text>
                      </Text>
                      <Text style={ListManageProductDetailStyles.reviews}>
                        (20) • $ {Ultis.money(item.Price)}
                      </Text>
                    </View>
                    {menu == 'Còn hàng' && (
                      <CustomSwitchComponent item={item} />
                    )}
                    {menu == TypeMenuPorduct.OutOfStock.name && (
                      <ReverseCustomSwitchComponent item={item} />
                    )}
                    {menu == TypeMenuPorduct.Pending.name && (
                      <CustomSwitchComponent item={item} />
                    )}
                    {menu == TypeMenuPorduct.Disable.name && (
                      <ReverseCustomSwitchComponent item={item} />
                    )}
                  </View>
                </View>
              </View>
              <View style={ListManageProductDetailStyles.reviewSell}>
                <View style={ListManageProductDetailStyles.statusContainer}>
                  <View style={ListManageProductDetailStyles.action}>
                    <View style={{marginRight: 10}}>
                      <AppSvg SvgSrc={iconSvg.instock} size={20} />
                    </View>
                    <Text style={{...TypoSkin.body3}}>
                      Tồn kho :
                      {item.status == TypeMenuPorduct.OutOfStock
                        ? 0
                        : item?.InStock
                        ? item?.InStock
                        : 0}
                    </Text>
                  </View>
                  <View style={ListManageProductDetailStyles.action}>
                    <View style={{marginRight: 10}}>
                      <AppSvg SvgSrc={iconSvg.like} size={20} />
                    </View>
                    <Text style={{...TypoSkin.body3}}>
                      Thích : {item?.Like ? item?.Like : 0}
                    </Text>
                  </View>
                </View>
                <View style={ListManageProductDetailStyles.statusContainer}>
                  <View style={ListManageProductDetailStyles.action}>
                    <View style={{marginRight: 10}}>
                      <AppSvg SvgSrc={iconSvg.wallet} size={20} />
                    </View>
                    <Text style={{...TypoSkin.body3}}>
                      Đã bán : {item?.Sold ? item?.Sold : 0}{' '}
                    </Text>
                  </View>
                  {menu && menu == TypeMenuPorduct.Violation.name && (
                    <TouchableOpacity
                      style={ListManageProductDetailStyles.action}
                      onPress={() => handleViewViolation(item?.ViolentNote)}>
                      <AppSvg SvgSrc={iconSvg.violent} size={20} />
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: 'blue',
                          marginLeft: 10,
                        }}>
                        Xem nội dung vi phạm
                      </Text>
                    </TouchableOpacity>
                  )}
                  {menu && menu == TypeMenuPorduct.Pending.name && (
                    <View style={ListManageProductDetailStyles.action}>
                      <AppSvg SvgSrc={iconSvg.violent} size={20} />
                      <Text
                        style={{
                          fontSize: 15,
                          color: '#FFD700',
                          marginLeft: 10,
                        }}>
                        Chờ duyệt
                      </Text>
                    </View>
                  )}
                </View>
              </View>
              <View style={ListManageProductDetailStyles.actionButtons}>
                <TouchableOpacity
                  style={ListManageProductDetailStyles.actionButton}
                  onPress={() => handleEditProduct(item)}>
                  <Text style={ListManageProductDetailStyles.actionButtonText}>
                    Sửa sản phẩm
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={ListManageProductDetailStyles.actionButton}
                  onPress={() => handleCopyProduct(item)}>
                  <Text style={ListManageProductDetailStyles.actionButtonText}>
                    Sao chép
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    ListManageProductDetailStyles.actionButton,
                    ListManageProductDetailStyles.deleteButton,
                  ]}
                  onPress={() => handleDeleteProduct(item)}>
                  <Text style={ListManageProductDetailStyles.actionButtonText}>
                    Xóa sản phẩm
                  </Text>
                </TouchableOpacity>
              </View>
            </Pressable>
          )}
        />
      </>
    );
  } else {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <EmptyPage />
      </View>
    );
  }
};
export default ManageProductDetail;
