/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {StyleSheet, TouchableOpacity, View, Text} from 'react-native';
import {ComponentStatus, showSnackbar, Winicon} from 'wini-mobile-components';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import FastImage from '@d11/react-native-fast-image';
import {useRoute} from '@react-navigation/native';
import {BaseDA} from '../../base/BaseDA';
import {useDispatch} from 'react-redux';
import ImagePicker from 'react-native-image-crop-picker';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import ConfigAPI from '../../Config/ConfigAPI';

const UserInfo = () => {
  const customer = useSelectorCustomerState().data;

  const [avt, setAvt] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch<any>();
  const [currentRank, setCurrentRank] = useState<any>();

  const {rankInfo, rankInfoLoading} = useSelectorCustomerState();

  useEffect(() => {
    if (customer && rankInfo && rankInfoLoading === false) {
      const currentRank = rankInfo?.achievedRank?.Score || 0;
      const ranksData = rankInfo?.RanksData || [];
      setCurrentRank(rankInfo?.achievedRank);
    }
  }, [customer, rankInfo, rankInfoLoading]);

  useEffect(() => {
    if (customer) {
      setAvt(customer.AvatarUrl);
    }
  }, [customer]);

  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: true,
      cropperCircleOverlay: true,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);
      if (resImgs) {
        setIsLoading(true);
        await dispatch(
          CustomerActions.edit({
            ...customer,
            AvatarUrl: resImgs[0].Id,
            RanksData: undefined,
          }),
        ).then(() => {
          setAvt(resImgs[0].Id);
          setIsLoading(false);
          showSnackbar({
            message: 'Cập nhật ảnh đại diện thành công',
            status: ComponentStatus.SUCCSESS,
            bottom: 60,
          });
          dispatch(CustomerActions.getInfor());
        });
      }
    }
  };
  return (
    <View style={styles.userInfo}>
      {customer ? (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            padding: 24,
            gap: 16,
            borderRadius: 8,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TouchableOpacity
              style={{
                width: 80,
                height: 80,
                borderRadius: 100,
                backgroundColor:
                  ColorThemes.light.neutral_main_background_color,
              }}
              onPress={customer ? pickerImg : undefined}>
              {avt?.includes('https') ? (
                <FastImage
                  source={{uri: avt}}
                  style={{width: '100%', height: '100%', borderRadius: 100}}
                />
              ) : avt ? (
                <FastImage
                  source={{uri: ConfigAPI.urlImg + avt}}
                  style={{width: '100%', height: '100%', borderRadius: 100}}
                />
              ) : (
                <View
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: 100,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                  }}>
                  <Text
                    style={{fontSize: 24, color: '#fff', fontWeight: 'bold'}}>
                    {customer?.Name
                      ? customer?.Name.charAt(0).toUpperCase()
                      : customer?.Email
                      ? customer?.Email.charAt(0).toUpperCase()
                      : ''}
                  </Text>
                </View>
              )}

              <View
                style={{
                  position: 'absolute',
                  padding: 5,
                  borderRadius: 24,
                  backgroundColor: '#fff',
                  right: -2,
                  bottom: -2,
                }}>
                <Winicon
                  src="fill/entertainment/camera"
                  size={10}
                  color={'#000'}
                />
              </View>
            </TouchableOpacity>
          </View>
          <View
            style={{
              gap: 4,
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
            }}>
            <Text
              style={{
                ...TypoSkin.heading6,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              {customer?.Name ?? customer?.Email ?? ''}
            </Text>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 6}}>
              {currentRank?.Icon ? (
                <FastImage
                  source={{
                    uri: ConfigAPI.urlImg + currentRank?.Icon,
                  }}
                  style={{width: 23, height: 23}}
                  resizeMode="contain"
                />
              ) : (
                <FastImage
                  source={require('../../assets/images/logo.png')}
                  style={{width: 23, height: 23}}
                  resizeMode="contain"
                />
              )}
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                  textAlign: 'center',
                }}>
                {currentRank?.Name
                  ? `Hạng ${currentRank?.Name}`
                  : 'Chưa có hạng'}
              </Text>
            </View>
          </View>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  userInfo: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 50,
    marginBottom: 8,
  },
  userName: {
    ...TypoSkin.title3,
    alignSelf: 'center',
  },
});

export default UserInfo;
