import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {AppliedFilters} from './components/types';
import CustomPicker from './components/CustomPicker';
import FilterTag from './components/FilterTag';
import PriceSlider from './components/PriceSlider';
import PrimaryButton from './components/PrimaryButton';
import Section from './components/Section';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {useProductFilter} from './hooks';
import {SORT_OPTIONS} from './components/constants';

interface PopupFilterProductProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: AppliedFilters) => void;
  parentCategory?: string;
}

export default function PopupFilterProduct({
  visible,
  onClose,
  onApply,
  parentCategory,
}: PopupFilterProductProps) {
  const {
    selectableCategories,
    selectableBrands,
    category,
    setCategory,
    brand,
    setBrand,
    sortOption,
    setSortOption,
    activeFilters,
    updateActiveFilter,
    price,
    setPrice,
    maxPrice,
    categoryLabel,
    brandLabel,
    sortOptionLabel,
    handleApplyFilters,
    handleResetFilters,
  } = useProductFilter({visible, onApply, onClose, parentCategory});

  return (
    <>
      <Modal
        animationType="slide"
        transparent={true}
        statusBarTranslucent={true}
        visible={visible}
        onRequestClose={onClose}>
        <Pressable style={styles.modalBackdrop} onPress={onClose} />
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Bộ lọc & Sắp xếp</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <Section title="Danh mục">
            <CustomPicker
              label={categoryLabel}
              data={selectableCategories}
              selectedValue={category}
              onSelect={setCategory}
              modalTitle="Chọn danh mục"
            />
          </Section>

          <Section title="Thương hiệu">
            <CustomPicker
              label={brandLabel}
              data={selectableBrands}
              selectedValue={brand}
              onSelect={setBrand}
              modalTitle="Chọn thương hiệu"
            />
          </Section>

          <Section title="Sắp xếp">
            <CustomPicker
              label={sortOptionLabel}
              data={SORT_OPTIONS}
              selectedValue={sortOption}
              onSelect={setSortOption}
              modalTitle="Sắp xếp theo"
            />
          </Section>

          <Section title="Bộ lọc">
            <View style={styles.filterTagGroup}>
              <FilterTag
                type="hot"
                label="HOT"
                isActive={activeFilters.IsHot ?? false}
                onPress={() => updateActiveFilter('IsHot', true)}
                backgroundColor={ColorThemes.light.secondary6_darker_color}
              />
              <FilterTag
                type="freeShip"
                label="Free ship"
                isActive={activeFilters.IsFreeShip ?? false}
                onPress={() => updateActiveFilter('IsFreeShip', true)}
                backgroundColor={ColorThemes.light.secondary2_main_color}
              />
            </View>
          </Section>

          <Section title="Giá">
            <PriceSlider
              value={price}
              onValueChange={setPrice}
              maxPrice={maxPrice}
            />
          </Section>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              onPress={handleResetFilters}
              style={styles.resetButton}>
              <Text style={styles.resetButtonText}>Làm lại</Text>
            </TouchableOpacity>

            <PrimaryButton title="Áp dụng" onPress={handleApplyFilters} />
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    paddingBottom: 30,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: 0,
    top: -5,
    backgroundColor: '#F0F0F0',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#888',
    fontWeight: 'bold',
  },
  filterTagGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    flex: 1,
  },
  resetButton: {
    paddingVertical: 15,
    borderRadius: 30,
    alignItems: 'center',
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  resetButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_text_body_color,
  },
});
