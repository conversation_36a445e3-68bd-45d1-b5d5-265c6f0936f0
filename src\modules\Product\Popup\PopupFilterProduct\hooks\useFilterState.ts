import {useState, useCallback} from 'react';
import {SORT_OPTIONS} from '../components/constants';
import {ActiveFilters} from '../components/types';

export const useFilterState = () => {
  const [category, setCategory] = useState<string | null>(null);
  const [brand, setBrand] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState(SORT_OPTIONS[0].id);
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({});

  const resetFilters = useCallback(() => {
    setCategory(null);
    setBrand(null);
    setSortOption(SORT_OPTIONS[0].id);
    setActiveFilters({});
  }, []);

  const updateActiveFilter = useCallback((filterKey: keyof ActiveFilters, value: boolean) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterKey]: value,
    }));
  }, []);

  return {
    category,
    setCategory,
    brand,
    setBrand,
    sortOption,
    setSortOption,
    activeFilters,
    setActiveFilters,
    updateActiveFilter,
    resetFilters,
  };
};
