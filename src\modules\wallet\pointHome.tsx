import React, {ImageBackground, Text, View, Animated} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {AppSvg, FDialog} from 'wini-mobile-components';
import iconSvg from '../../svg/icon';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {useEffect, useState, useRef} from 'react';
import {DataController} from '../../base/baseController';
import {
  TransactionType,
  TransactionStatus,
  MissionType,
} from '../../Config/Contanst';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from '@d11/react-native-fast-image';
import {TouchableOpacity} from 'react-native';
import {RootScreen} from '../../router/router';
import {useNavigation} from '@react-navigation/native';
import {Ultis} from '../../utils/Utils';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {getRankCustomer} from '../../redux/actions/customerAction';
import {useDispatch, useSelector} from 'react-redux';
import {RootState} from '../../redux/store/store';
import WalletDA from './da';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

// Skeleton Component
const SkeletonBox = ({
  width,
  height,
  style,
}: {
  width: any;
  height: number;
  style?: any;
}) => {
  return (
    <SkeletonPlaceholder
      backgroundColor="#f0f0f0"
      highlightColor="#e0e0e0"
      children={
        <SkeletonPlaceholder.Item
          width={typeof width === 'string' ? undefined : width}
          height={height}
          borderRadius={4}
          style={style}
        />
      }
    />
  );
};

const PointHome = () => {
  const dispatch = useDispatch<any>();
  const {rankInfo, rankInfoLoading} = useSelector(
    (state: RootState) => state.customer,
  );
  const customer = useSelectorCustomerState().data;
  const [isVip, setIsVip] = useState(false);
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  useEffect(() => {
    if (customer?.Id) {
      dispatch(getRankCustomer({Id: customer.Id}));
    }
  }, [customer?.Id]);

  useEffect(() => {
    //focus effect
    const unsubscribe = navigation.addListener('focus', async () => {
      if (!customer?.Id) return;
      dispatch(getRankCustomer({Id: customer.Id}));
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (rankInfo?.achievedRank) {
      setIsVip(Number(rankInfo?.achievedRank?.Sort) > 1);
    }
  }, [rankInfo?.achievedRank]);
  useEffect(() => {
    if (rankInfo?.achievedRank) {
      const walletda = new WalletDA();
      // #region xử lý nhiệm vụ
      walletda.CaculateMisson(customer?.Id, MissionType.Login, rankInfo);
    }
  }, [rankInfo]);
  const getColorVip = () => {
    if (isVip) {
      return ['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF'];
    }
    return ['#90C8FB', '#8DC4F7E5', '#B6F5FE'];
  };

  const getImageVip = () => {
    if (isVip) {
      return require('../../assets/bg_circle_orange.png');
    }
    return require('../../assets/bg04.png');
  };

  const getColorText = () => {
    if (isVip) {
      return ColorThemes.light.secondary6_darker_color;
    }
    return ColorThemes.light.primary_main_color;
  };

  return (
    <View
      style={{
        height: 82,
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginTop: 16,
        position: 'relative',
        borderRadius: 10,
        overflow: 'hidden',
      }}>
      <FDialog ref={dialogRef} />
      {rankInfoLoading ? (
        <SkeletonBox width={'100%'} height={82} />
      ) : (
        <LinearGradient
          colors={getColorVip()}
          start={{x: 0, y: 0}}
          end={{x: 0, y: 1}}
          style={{
            borderRadius: 10,
            height: '100%',
            flex: 1,
            overflow: 'hidden',
            width: '100%',
          }}>
          <ImageBackground
            source={getImageVip()}
            resizeMode="contain"
            style={{
              position: 'absolute',
              bottom: -30,
              left: -20,
              width: 110,
              height: 110,
            }}
          />
          <View style={{paddingLeft: 76, paddingTop: 16}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
              {rankInfoLoading ? (
                <SkeletonBox width={80} height={20} />
              ) : (
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {Ultis.money(rankInfo?.totalReward || 0)} điểm
                </Text>
              )}
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              {rankInfoLoading ? (
                <>
                  <SkeletonBox
                    width={20}
                    height={20}
                    style={{borderRadius: 10}}
                  />
                  <SkeletonBox width={100} height={20} />
                </>
              ) : (
                <>
                  {rankInfo?.achievedRank?.Icon ? (
                    <FastImage
                      source={{
                        uri: ConfigAPI.urlImg + rankInfo?.achievedRank?.Icon,
                      }}
                      style={{width: 20, height: 20}}
                      resizeMode="contain"
                    />
                  ) : (
                    <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                  )}
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    Hạng {rankInfo?.achievedRank?.Name || 'Chưa có hạng'}
                  </Text>
                </>
              )}
            </View>
          </View>

          <TouchableOpacity
            style={{position: 'absolute', right: 16, top: 22}}
            onPress={() => {
              if (!customer?.Id) {
                dialogCheckAcc(dialogRef);
                return;
              }
              navigation.push(RootScreen.GiftExchange);
            }}>
            <LinearGradient
              start={{x: 0, y: 0}}
              end={{x: 0, y: 1}}
              colors={['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF']}
              style={{borderRadius: 8, minHeight: 32}}>
              <Text
                style={{
                  ...TypoSkin.body2,
                  color: getColorText(),
                  fontSize: 14,
                  fontWeight: '500',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                }}>
                Đổi quà
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </LinearGradient>
      )}
    </View>
  );
};

export default PointHome;
