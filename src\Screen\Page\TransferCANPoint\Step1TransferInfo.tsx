import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {AppButton, ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import RecipientSelector from '../../../components/RecipientSelector';
import PointAmountInput from '../../../components/PointAmountInput';
import {TransactionStatus, TransactionType} from '../../../Config/Contanst';
import {DataController} from '../../../base/baseController';
import store from '../../../redux/store/store';

interface Step1TransferInfoProps {
  currentPoints: number;
  transferAmount: string;
  recipientName?: string;
  recipientPhone?: string;
  onAmountChange: (amount: string) => void;
  onSelectRecipient: () => void;
  onNext: () => void;
  type: TransactionType;
}

const Step1TransferInfo: React.FC<Step1TransferInfoProps> = ({
  currentPoints,
  transferAmount,
  recipientName,
  recipientPhone,
  onAmountChange,
  onSelectRecipient,
  onNext,
  type,
}) => {
  const isValidAmount = () => {
    const amount = parseInt(transferAmount || '0');
    return amount > 0 && amount <= currentPoints;
  };

  const isFormValid = () => {
    if (type === TransactionType.Withdraw) return isValidAmount();
    if (type === TransactionType.tranfer)
      return recipientName && recipientPhone && isValidAmount();
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled">
        {type === TransactionType.tranfer && (
          <RecipientSelector
            recipientName={recipientName}
            recipientPhone={recipientPhone}
            onPress={onSelectRecipient}
          />
        )}

        <PointAmountInput
          currentPoints={currentPoints}
          transferAmount={transferAmount}
          onAmountChange={onAmountChange}
          type={type}
        />
      </ScrollView>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Tiếp theo"
          onPress={async () => {
            const controller = new DataController('HistoryReward');
            const customer = store.getState().customer.data;
            const res = await controller.getListSimple({
              query: `@CustomerId: {${customer?.Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
              sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
              returns: ['Value'],
            });
            if (res?.code === 200 && res?.data.length) {
              // get total from res.data with field Value
              const total = res.data.reduce(
                (total: number, item: any) => total + parseFloat(item.Value),
                0,
              );
              if (total < parseFloat(transferAmount)) {
                showSnackbar({
                  message: 'Số điểm không đủ để thực hiện giao dịch',
                  status: ComponentStatus.ERROR,
                });
                return;
              } else {
                if (onNext) onNext();
              }
            } else {
              showSnackbar({
                message: 'Có lỗi xảy ra khi lấy thông tin số dư ví của bạn',
                status: ComponentStatus.ERROR,
              });
            }
          }}
          disabled={!isFormValid()}
          backgroundColor={
            isFormValid()
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          containerStyle={styles.button}
          borderColor="transparent"
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
    width: '80%',
    margin: 'auto',
  },
});

export default Step1TransferInfo;
