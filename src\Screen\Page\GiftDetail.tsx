import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import {useRoute, useNavigation} from '@react-navigation/native';
import FastImage from '@d11/react-native-fast-image';
import {
  AppSvg,
  ComponentStatus,
  FLoading,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import HeaderShop from '../../modules/shop/component/HeaderShop';

import GiftDA, {GiftItem} from '../../modules/gift/giftDA';
import ConfigAPI from '../../Config/ConfigAPI';
import {InforHeader} from '../Layout/headers/inforHeader';
import RenderHTML from 'react-native-render-html';
import iconSvg from '../../svg/icon';
import {Ultis} from '../../utils/Utils';

const {width} = Dimensions.get('window');

const GiftDetail = () => {
  const route = useRoute<any>();
  const navigation = useNavigation();
  const customer = useSelectorCustomerState().data;
  const giftDA = new GiftDA();

  const {giftId} = route.params;

  // States
  const [loading, setLoading] = useState(false);
  const [gift, setGift] = useState<GiftItem | null>(null);
  const [currentPoints, setCurrentPoints] = useState(0);

  // Fetch gift detail
  const fetchGiftDetail = async () => {
    if (!giftId) return;

    setLoading(true);
    try {
      const giftData = await giftDA.getGiftDetail(giftId);
      if (giftData) {
        setGift(giftData);
      } else {
        showSnackbar({
          message: 'Không thể tải thông tin quà tặng',
          status: ComponentStatus.ERROR,
        });
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error fetching gift detail:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải thông tin quà tặng',
        status: ComponentStatus.ERROR,
      });
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // Fetch current points
  const fetchCurrentPoints = async () => {
    if (!customer?.Id) return;
    try {
      const points = await giftDA.getCurrentPoints(customer.Id);
      setCurrentPoints(points);
    } catch (error) {
      console.error('Error fetching current points:', error);
    }
  };

  // Handle exchange gift
  const handleExchangeGift = async () => {
    if (!customer?.Id || !gift) return;

    if (currentPoints < gift.Value) {
      showSnackbar({
        message: 'Số điểm không đủ để đổi quà này',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    const isExpired =
      parseFloat(gift.ExpriseDate?.toString() ?? '0') < new Date().getTime();
    const isOutOfStock = gift.Quantity !== undefined && gift.Quantity <= 0;

    if (isExpired) {
      showSnackbar({
        message: 'Quà tặng này đã hết hạn',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    if (isOutOfStock) {
      showSnackbar({
        message: 'Quà tặng này đã hết hàng',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    Alert.alert(
      'Xác nhận đổi quà',
      `Bạn có chắc chắn muốn đổi "${gift.Name}" với ${gift.Value} điểm?`,
      [
        {text: 'Hủy', style: 'cancel'},
        {
          text: 'Đồng ý',
          onPress: async () => {
            try {
              setLoading(true);
              const response = await giftDA.exchangeGift(
                gift.Id!,
                customer.Id,
                gift.Value,
                gift.Name!,
              );

              if (response?.code === 200) {
                showSnackbar({
                  message: 'Đổi quà thành công! Vui lòng chờ duyệt.',
                  status: ComponentStatus.SUCCSESS,
                });

                // Refresh current points
                await fetchCurrentPoints();

                // Navigate back
                navigation.goBack();
              } else {
                showSnackbar({
                  message: 'Không thể đổi quà. Vui lòng thử lại.',
                  status: ComponentStatus.ERROR,
                });
              }
            } catch (error) {
              console.error('Error exchanging gift:', error);
              showSnackbar({
                message: 'Có lỗi xảy ra khi đổi quà',
                status: ComponentStatus.ERROR,
              });
            } finally {
              setLoading(false);
            }
          },
        },
      ],
    );
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  useEffect(() => {
    if (customer?.Id) {
      fetchGiftDetail();
      fetchCurrentPoints();
    }
  }, [giftId, customer?.Id]);

  if (!gift) {
    return (
      <View style={styles.container}>
        <FLoading visible={loading} />
        <InforHeader title="Chi tiết quà tặng" />
      </View>
    );
  }

  const canExchange = currentPoints >= gift.Value;

  return (
    <View style={styles.container}>
      <FLoading visible={loading} />

      {/* Header */}
      <InforHeader title="Chi tiết quà tặng" />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Image */}
        <View style={styles.imageContainer}>
          <FastImage
            source={{uri: ConfigAPI.urlImg + gift.Img}}
            style={styles.image}
            resizeMode={FastImage.resizeMode.cover}
          />
        </View>

        {/* Content */}
        <View style={styles.detailContent}>
          {/* Title */}
          <Text style={styles.title}>{gift.Name}</Text>

          {/* Points */}
          <View style={styles.pointsContainer}>
            <AppSvg SvgSrc={iconSvg.diamonIcon} size={16} />
            <Text style={styles.points}>{gift.Value}</Text>
          </View>

          {/* Info */}
          <View style={styles.infoContainer}>
            {gift.ExpriseDate && (
              <View style={styles.infoRow}>
                <Winicon
                  src="outline/time/calendar"
                  size={16}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <Text style={styles.infoText}>
                  Hết hạn:{' '}
                  {Ultis.formatDateTime(
                    parseFloat(gift.ExpriseDate?.toString() ?? '0'),
                  )}
                </Text>
              </View>
            )}
          </View>

          {/* Description */}
          {gift.Description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionTitle}>Thông tin chi tiết</Text>
              <RenderHTML
                contentWidth={width}
                source={{html: gift.Description}}
                tagsStyles={{
                  body: {
                    color: '#313135',
                    fontSize: 14,
                    lineHeight: 20,
                    fontFamily: 'Inter',
                  },
                }}
              />
            </View>
          )}
        </View>
      </ScrollView>

      {/* Exchange Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.exchangeButton, !canExchange && styles.disabledButton]}
          onPress={handleExchangeGift}
          disabled={!canExchange}>
          <Text
            style={[
              styles.exchangeButtonText,
              !canExchange && styles.disabledButtonText,
            ]}>
            {!canExchange ? 'Không đủ điểm' : 'Đổi ngay'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    backgroundColor: 'white',
  },
  image: {
    width: width,
    height: width * 0.8,
  },
  badge: {
    position: 'absolute',
    top: 16,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  detailContent: {
    backgroundColor: 'white',
  },
  title: {
    ...TypoSkin.heading2,
    color: ColorThemes.light.neutral_text_title_color,
    // marginBottom: 16,
    paddingHorizontal: 20,
    fontSize: 18,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 20,
  },
  points: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  infoContainer: {
    // marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    // marginBottom: 8,
  },
  infoText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 14,
  },
  descriptionContainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  descriptionTitle: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
    fontSize: 16,
  },
  description: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    lineHeight: 24,
  },
  footer: {
    backgroundColor: 'white',
  },
  exchangeButton: {
    paddingVertical: 16,
    borderRadius: 40,
    alignItems: 'center',
    width: '80%',
    margin: 'auto',
    marginBottom: 20,
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  disabledButton: {
    backgroundColor: '#E0E0E0',
  },
  exchangeButtonText: {
    ...TypoSkin.title3,
    color: 'white',
    fontWeight: '600',
  },
  disabledButtonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default GiftDetail;
