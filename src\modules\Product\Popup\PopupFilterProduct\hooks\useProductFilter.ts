import {useEffect, useCallback} from 'react';
import {useCategories} from './useCategories';
import {useBrands} from './useBrands';
import {usePrice} from './usePrice';
import {useFilterState} from './useFilterState';
import {AppliedFilters, SelectableItem} from '../components/types';
import {Category} from '../../../../../redux/models/category';
import {Brand} from '../../../../../redux/models/brand';
import {SORT_OPTIONS} from '../components/constants';

const mapToSelectableItem = (items: (Category | Brand)[]): SelectableItem[] => {
  return items.map(item => ({id: item.Id, name: item.Name}));
};

interface UseProductFilterProps {
  visible: boolean;
  onApply: (filters: AppliedFilters) => void;
  onClose: () => void;
  parentCategory?: string;
}

export const useProductFilter = ({
  visible,
  onApply,
  onClose,
  parentCategory,
}: UseProductFilterProps) => {
  const {categories, fetchCategories} = useCategories(parentCategory);
  const {brands, fetchBrands} = useBrands();
  const {price, setPrice, maxPrice, getProductMaxPrice, resetPrice} =
    usePrice(parentCategory);
  const {
    category,
    setCategory,
    brand,
    setBrand,
    sortOption,
    setSortOption,
    activeFilters,
    setActiveFilters,
    updateActiveFilter,
    resetFilters,
  } = useFilterState();

  // Fetch data when modal becomes visible
  useEffect(() => {
    if (visible) {
      fetchCategories();
      fetchBrands();
      getProductMaxPrice();
    }
  }, [visible, fetchCategories, fetchBrands, getProductMaxPrice]);

  // Handle apply filters
  const handleApplyFilters = useCallback(() => {
    const data: AppliedFilters = {
      categoryId: category,
      brandId: brand,
      sortOption,
      activeFilters,
      maxPrice: price,
    };
    console.log('🚀 ~ handleApplyFilters ~ data:', data);

    onApply(data);
    onClose();
  }, [category, brand, sortOption, activeFilters, price, onApply, onClose]);

  // Handle reset all filters
  const handleResetFilters = useCallback(() => {
    resetFilters();
    resetPrice();
  }, [resetFilters, resetPrice]);

  // Computed values
  const selectableCategories = mapToSelectableItem(categories);
  const selectableBrands = mapToSelectableItem(brands);

  const categoryLabel =
    selectableCategories.find(c => c.id === category)?.name || 'Chọn danh mục';
  const brandLabel =
    selectableBrands.find(b => b.id === brand)?.name || 'Chọn thương hiệu';
  const sortOptionLabel =
    SORT_OPTIONS.find(s => s.id === sortOption)?.name || '';

  return {
    // Data
    selectableCategories,
    selectableBrands,

    // Filter states
    category,
    setCategory,
    brand,
    setBrand,
    sortOption,
    setSortOption,
    activeFilters,
    setActiveFilters,
    updateActiveFilter,

    // Price
    price,
    setPrice,
    maxPrice,

    // Labels
    categoryLabel,
    brandLabel,
    sortOptionLabel,

    // Actions
    handleApplyFilters,
    handleResetFilters,
  };
};
