import {useSelector, useDispatch} from 'react-redux';
import {useCallback} from 'react';
import {fetchBrands} from '../../../../../redux/actions/brandAction';
import {RootState} from '../../../../../redux/store/store';

export const useBrands = () => {
  const dispatch = useDispatch<any>();
  const {data: brands, loading} = useSelector((state: RootState) => state.brand);

  const fetchBrandsData = useCallback(() => {
    dispatch(fetchBrands());
  }, [dispatch]);

  return {
    brands,
    loading,
    fetchBrands: fetchBrandsData,
  };
};
