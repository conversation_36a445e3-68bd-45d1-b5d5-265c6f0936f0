/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  ListTile,
  Rating,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useForm} from 'react-hook-form';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {TextFieldForm} from '../form/component-form';
import {postCommentsActions} from '../../community/reducers/postCommentsReducer';
import {newsFeedActions} from '../../community/reducers/newsFeedReducer';
import {Ultis} from '../../../utils/Utils';
import Clipboard from '@react-native-clipboard/clipboard';
import FastImage from '@d11/react-native-fast-image';

interface Props {
  containerStyle?: ViewStyle;
  mainContainerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  ListComment?: [];
  data: any;
  actionView?: React.ReactNode;
  trailingView?: React.ReactNode;
  onReply?: (data: any) => void;
  onPressTitle?: () => void;
}

export function DefaultComment(props: Props) {
  const [isExpanded, setIsExpanded] = useState(false);

  const isShortContent = props.data?.Content?.length <= 50;
  const dispatch: AppDispatch = useDispatch();
  const toggleExpand = () => {
    setIsExpanded(!isExpanded); // Toggle between expanded and collapsed state
  };

  const user = useSelectorCustomerState().data;
  const [showReplyField, setShowReplyField] = useState(false); // Add this state
  const [showReplies, setShowReplies] = useState(false); // Add this state
  return (
    <ListTile
      style={{
        ...props.containerStyle,
        alignItems: 'flex-start',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}
      listtileStyle={{alignItems: 'flex-start', gap: 8}}
      isClickLeading
      leading={
        <TouchableOpacity onPress={props.onPressTitle} style={{flex: 1}}>
          <View
            style={[
              {
                width: 40,
                height: 40,
                borderRadius: 100,
              },
            ]}>
            <FastImage
              key={props.data?.relativeUser?.image}
              source={{
                uri: props.data?.relativeUser?.image
                  ? `${ConfigAPI.urlImg + props.data.relativeUser?.image}`
                  : 'https://placehold.co/40/FFFFFF/000000/png',
              }}
              style={{width: '100%', height: '100%', borderRadius: 100}}
            />
          </View>
        </TouchableOpacity>
      }
      title={
        <Text
          onPress={props.onPressTitle}
          style={{
            ...TypoSkin.heading8,
            color: ColorThemes.light.neutral_text_title_color,
          }}>
          {props.data.relativeUser?.title ?? ''}
        </Text>
      }
      trailing={props.trailingView}
      subtitle={
        <View style={{paddingTop: 2, flex: 1, width: '100%'}}>
          <Text
            style={{
              ...TypoSkin.subtitle4,
              color: ColorThemes.light.neutral_text_subtitle_color,
              paddingBottom: 4,
            }}>
            {props.data.relativeUser?.subtitle &&
            typeof props.data.relativeUser.subtitle === 'number'
              ? Ultis.getDiffrentTime(props.data.relativeUser.subtitle)
              : props.data.relativeUser.subtitle ?? ''}
          </Text>
          {props.data.Content ? (
            <TouchableOpacity
              onLongPress={() => {
                Clipboard.setString(`${props.data.Content}`);
                showSnackbar({
                  message: 'Đã sao chép nội dung',
                  status: ComponentStatus.SUCCSESS,
                });
              }}
              onPress={
                isShortContent
                  ? undefined
                  : () => {
                      if (!isShortContent) toggleExpand();
                    }
              }
              style={{paddingTop: props.data.Description ? 0 : 12}}>
              <Text style={[stylesDefault.bodyContentStyle]}>
                {isShortContent
                  ? props.data.Content
                  : isExpanded
                  ? props.data.Content
                  : `${props.data.Content.substring(0, 100)}...`}{' '}
                {/* Show truncated text or full text */}
              </Text>
              {isShortContent ? null : (
                <AppButton
                  title={isExpanded ? 'Thu gọn' : 'Xem thêm'}
                  containerStyle={{
                    justifyContent: 'flex-start',
                    alignSelf: 'baseline',
                    marginVertical: 8,
                  }}
                  backgroundColor={'transparent'}
                  textStyle={TypoSkin.buttonText3}
                  borderColor="transparent"
                  onPress={toggleExpand}
                  textColor={ColorThemes.light.infor_main_color}
                />
              )}
            </TouchableOpacity>
          ) : null}
          <View
            style={{
              flex: 1,
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
            }}>
            <View
              style={{
                flexDirection: 'row',
                paddingTop: 16,
                alignItems: 'center',
                gap: 8,
                paddingBottom: 16,
              }}>
              <AppButton
                backgroundColor={
                  ColorThemes.light.neutral_main_background_color
                }
                borderColor="transparent"
                containerStyle={{
                  padding: 4,
                  height: 24,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                }}
                onPress={async () => {
                  if (user) {
                    dispatch(
                      postCommentsActions.toggleLike(
                        props.data?.PostId,
                        props.data?.Id,
                        props.data?.IsLike === true,
                      ),
                    );
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                  }
                }}
                title={
                  <Text
                    style={{
                      ...TypoSkin.buttonText5,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    {props.data?.Likes ?? 0}
                  </Text>
                }
                textColor={
                  props.data?.IsLike === true
                    ? ColorThemes.light.error_main_color
                    : ColorThemes.light.neutral_text_subtitle_color
                }
                prefixIconSize={12}
                prefixIcon={
                  props.data?.IsLike === true
                    ? 'fill/emoticons/heart'
                    : 'outline/emoticons/heart'
                }
              />
              {!props.data.ParentId && props.onReply ? (
                <AppButton
                  backgroundColor={
                    ColorThemes.light.neutral_main_background_color
                  }
                  borderColor="transparent"
                  containerStyle={{
                    padding: 4,
                    height: 24,
                    paddingVertical: 0,
                    paddingHorizontal: 8,
                  }}
                  onPress={() => {
                    setShowReplyField(!showReplyField);
                    if (props.onReply) props.onReply(props.data);
                  }} // Toggle reply field
                  prefixIconSize={12}
                  prefixIcon={'outline/arrows/reply-all'}
                  textColor={ColorThemes.light.neutral_text_subtitle_color}
                  title={'Reply'}
                  textStyle={{...TypoSkin.buttonText5}}
                />
              ) : null}
            </View>
          </View>
          {/* comments children */}
          {!props.data.ParentId && props.ListComment?.length ? (
            <TouchableOpacity onPress={() => setShowReplies(!showReplies)}>
              <Text style={{...TypoSkin.body3}}>
                {showReplies ? 'Ẩn' : 'Hiện'} {props.ListComment?.length} phản
                hồi
              </Text>
            </TouchableOpacity>
          ) : null}
          {showReplies && !props.data.ParentId && props.ListComment?.length ? (
            <View style={{paddingTop: 16}}>
              {!props.data.ParentId &&
                props.ListComment?.map((childItem: any, index: any) => {
                  return (
                    <DefaultComment
                      key={index}
                      data={{
                        ...childItem,
                        relativeUser: {
                          title: 'Người dùng Chanivo',
                          subtitle: 'Thời gian',
                          ...childItem.relativeUser,
                        },
                      }}
                      containerStyle={{padding: 0}}
                      actionView={
                        <View
                          style={{
                            flexDirection: 'row',
                            paddingTop: 16,
                            alignItems: 'center',
                            gap: 8,
                          }}>
                          <View
                            style={{
                              flex: 1,
                              flexDirection: 'row',
                              gap: 4,
                              justifyContent: 'flex-start',
                              alignItems: 'center',
                            }}>
                            <AppButton
                              backgroundColor={
                                ColorThemes.light.neutral_main_background_color
                              }
                              borderColor="transparent"
                              containerStyle={{
                                padding: 4,
                                height: 24,
                                paddingVertical: 0,
                                paddingHorizontal: 8,
                              }}
                              onPress={async () => {
                                if (user) {
                                  dispatch(
                                    postCommentsActions.toggleLike(
                                      props.data?.PostId,
                                      childItem.Id,
                                      childItem.IsLike === true,
                                    ),
                                  );
                                } else {
                                  ///TODO: check chưa login thì confirm ra trang login
                                }
                              }}
                              title={
                                <Text
                                  style={{
                                    ...TypoSkin.buttonText5,
                                    color:
                                      ColorThemes.light
                                        .neutral_text_subtitle_color,
                                  }}>
                                  {childItem.Likes ?? 0}
                                </Text>
                              }
                              textColor={
                                childItem.IsLike === true
                                  ? ColorThemes.light.error_main_color
                                  : ColorThemes.light
                                      .neutral_text_subtitle_color
                              }
                              prefixIconSize={12}
                              prefixIcon={
                                childItem.IsLike === true
                                  ? 'fill/emoticons/heart'
                                  : 'outline/emoticons/heart'
                              }
                            />
                          </View>
                        </View>
                      }
                    />
                  );
                })}
            </View>
          ) : null}
          {/* Show reply field when showReplyField is true */}
          {showReplies && !props.data.ParentId && (
            <TouchableOpacity
              onPress={async () => {
                if (user) if (props.onReply) props.onReply(props.data);
                // if (user) {
                //   if (methods.getValues().Comment) {
                //      dispatch(
                //       postCommentsActions.addNewComment(
                //         props.data?.PostId,
                //         methods.getValues().Comment,
                //         props.data?.Id,
                //       ),
                //     );
                //     dispatch(newsFeedActions.updateCommentCount(props.data?.PostId, 1));
                //     methods.setValue('Comment', undefined);
                //     // setShowReplyField(false); // Hide reply field after sending
                //   }
                // } else {
                //   ///TODO: check chưa login thì confirm ra trang login
                // }
              }}
              style={{
                flexDirection: 'row',
                paddingBottom: 16,
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  paddingHorizontal: 16,
                  height: 40,
                  borderColor: ColorThemes.light.neutral_main_border_color,
                  borderWidth: 1,
                  flex: 1,
                  borderRadius: 8,
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  Viết phản hồi...
                </Text>
              </View>

              <AppButton
                prefixIcon={'fill/user interface/send-message'}
                prefixIconSize={24}
                backgroundColor={ColorThemes.light.transparent}
                borderColor="transparent"
                containerStyle={{
                  paddingHorizontal: 12,
                  height: 45,
                }}
                textColor={ColorThemes.light.primary_main_color}
              />
            </TouchableOpacity>
          )}
        </View>
      }
    />
  );
}

export function SkeletonPlaceComment() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{padding: 16, gap: 12}}>
        {/* Header with avatar and name */}
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 12}}>
          {/* Avatar */}
          <View style={{width: 40, height: 40, borderRadius: 20}} />

          {/* Name and time */}
          <View style={{gap: 4}}>
            <View style={{width: 80, height: 14, borderRadius: 4}} />
            <View style={{width: 60, height: 12, borderRadius: 4}} />
          </View>
        </View>

        {/* Post content */}
        <View style={{gap: 8}}>
          <View style={{width: '100%', height: 16, borderRadius: 4}} />
          <View style={{width: '90%', height: 16, borderRadius: 4}} />
        </View>

        {/* Actions */}
        <View style={{flexDirection: 'row', gap: 16, marginTop: 8}}>
          <View style={{width: 60, height: 20, borderRadius: 4}} />
          <View style={{width: 60, height: 20, borderRadius: 4}} />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
});
