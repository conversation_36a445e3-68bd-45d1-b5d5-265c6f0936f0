import React from 'react';
import {View, Text, TouchableOpacity, Image} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {DescriptionProps} from '../../../components/dto/dto';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import FastImage from '@d11/react-native-fast-image';
import {DescriptionImageStyles} from '../styles/DescriptionImageStyles';

const DescriptionImage = (props: DescriptionProps) => {
  const {image, pickerImg, avataProduct, checkImage, deleteImage} = props;
  return (
    <View style={DescriptionImageStyles.section}>
      <Text style={DescriptionImageStyles.label}>
        Hình ảnh sản phẩm * ({checkImage?.length ? checkImage?.length : 0}/5)
      </Text>
      <ScrollView style={DescriptionImageStyles.detail} horizontal={true}>
        {image &&
          image.length > 0 &&
          image.map((item, index) => (
            <View key={index} style={{position: 'relative'}}>
              <View style={DescriptionImageStyles.imageContent}>
                <FastImage
                  source={{uri: item.path}}
                  style={DescriptionImageStyles.image}
                />
              </View>
              <TouchableOpacity
                onPress={() => deleteImage(index)}
                style={DescriptionImageStyles.cancelImageButton}>
                <AppSvg SvgSrc={iconSvg.exit} size={24} />
              </TouchableOpacity>
            </View>
          ))}
      </ScrollView>
      <TouchableOpacity
        style={DescriptionImageStyles.imagePlaceholder}
        onPress={pickerImg}>
        <Text style={DescriptionImageStyles.placeholderText}>Thêm ảnh</Text>
      </TouchableOpacity>
      <View style={DescriptionImageStyles.avata}>
        {avataProduct && (
          <View>
            <Image
              source={{uri: avataProduct}}
              style={DescriptionImageStyles.avataImage}
            />
            <Text style={DescriptionImageStyles.avataText}>Ảnh đại diện</Text>
          </View>
        )}
      </View>
    </View>
  );
};
export default DescriptionImage;
