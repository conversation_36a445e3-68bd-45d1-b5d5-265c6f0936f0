import {
  ScrollView,
  Text,
  View,
  Image,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {useRoute} from '@react-navigation/native';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {ComponentStatus, showSnackbar, Winicon} from 'wini-mobile-components';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from '@d11/react-native-fast-image';
import {stringify} from 'uuid';
import {useCallback} from 'react';
import {handleShopInfoPress} from '../../modules/Product/Component/Ultis';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ChatAPI from '../../modules/chat/services/ChatAPI';
import {addChatRoom} from '../../redux/reducers/ChatReducer';
import store, {AppDispatch} from '../../redux/store/store';
import {navigate, RootScreen} from '../../router/router';
import {useDispatch} from 'react-redux';

export default function InforShopView() {
  const route = useRoute<any>();
  const shop = route?.params?.shop;
  const customer = useSelectorCustomerState().data;
  const dispatch: AppDispatch = useDispatch();

  const handleCall = (phoneNumber: string) => {
    if (phoneNumber) {
      //   Linking.openURL(`tel:${phoneNumber}`);
    }
  };

  const handleMessage = async (phoneNumber: string) => {
    try {
      if (!customer?.Id && !customer?.id) {
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể xác định người dùng hiện tại',
        });
        return;
      }

      if (!shop?.Id) {
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không tìm thấy thông tin cửa hàng',
        });
        return;
      }

      // Tạo contact object từ thông tin shop
      const contact = {
        Id: shop.Id,
        Name: shop.Name || 'Cửa hàng',
        AvatarUrl: shop.Img || null,
      };

      const currentUserId = customer.Id || customer.id!;

      // Tìm hoặc tạo ChatRoom
      const chatRoom = await ChatAPI.findOrCreatePrivateRoom(
        currentUserId,
        contact,
      );
      const listRoom = store.getState().chat.rooms;
      if (!listRoom.find((item: any) => item.id === chatRoom.id)) {
        dispatch(addChatRoom(chatRoom));
      }

      // Navigate đến ChatRoomScreen
      navigate(RootScreen.ChatRoom, {room: chatRoom});
    } catch (error) {
      console.error('Error creating chat:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tạo cuộc trò chuyện',
      });
    }
  };

  const handleShopInfoPressLocal = useCallback(
    (type: 'rating' | 'product' | 'order') => {
      handleShopInfoPress(type, shop);
    },
    [shop],
  );

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader title={'Thông tin cửa hàng'} />
      <ScrollView style={{flex: 1}}>
        {/* Shop Profile Section */}
        <View
          style={{
            alignItems: 'center',
            paddingVertical: 24,
            backgroundColor: 'white',
          }}>
          {/* Avatar */}
          <View
            style={{
              width: 100,
              height: 100,
              borderRadius: 50,
              backgroundColor: '#E8F5E8',
              marginBottom: 16,
              overflow: 'hidden',
            }}>
            {shop?.Img ? (
              <FastImage
                source={{uri: shop.Img}}
                style={{width: 100, height: 100}}
                resizeMode="cover"
              />
            ) : (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Winicon
                  src="fill/shopping/store"
                  size={40}
                  color={ColorThemes.light.primary_main_color}
                />
              </View>
            )}
          </View>

          {/* Shop Name */}
          <Text
            style={{
              ...TypoSkin.heading6,
              color: ColorThemes.light.neutral_text_title_color,
              marginBottom: 8,
            }}>
            {shop?.Name || 'Tên cửa hàng'}
          </Text>

          {/* Rating and Status */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
            }}>
            <View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: '#4CAF50',
                marginRight: 6,
              }}
            />
            <Text
              style={{
                ...TypoSkin.regular3,
                color: '#4CAF50',
              }}>
              Online
            </Text>
          </View>

          {/* Action Buttons */}
          <View
            style={{
              flexDirection: 'row',
              gap: 20,
            }}>
            <TouchableOpacity
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                backgroundColor: ColorThemes.light.primary_main_color,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => handleCall(shop?.Mobile)}>
              <Winicon
                src="fill/user interface/phone-call"
                size={24}
                color="white"
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                backgroundColor: ColorThemes.light.primary_main_color,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => handleMessage(shop?.Mobile)}>
              <Winicon
                src="fill/social media/logo-messenger"
                size={24}
                color="white"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Shop Details Section */}
        <View
          style={{
            backgroundColor: 'white',
            marginTop: 8,
            paddingHorizontal: 20,
            paddingVertical: 16,
          }}>
          {/* Contact Person */}

          {/* Owner Name */}
          {shop?.OwnerName && (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
                borderBottomWidth: 1,
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
              }}>
              <Winicon
                src="fill/users/profile"
                size={20}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
              <Text
                style={{
                  ...TypoSkin.regular2,
                  color: ColorThemes.light.neutral_text_title_color,
                  marginLeft: 16,
                }}>
                {shop?.OwnerName || 'Chủ shop'}
              </Text>
            </View>
          )}

          {/* Phone Number */}
          {shop?.Mobile && (
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
                borderBottomWidth: 1,
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
              }}
              onPress={() => handleCall(shop.Mobile)}>
              <Winicon
                src="fill/user interface/phone-call"
                size={20}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
              <Text
                style={{
                  ...TypoSkin.regular2,
                  color: ColorThemes.light.neutral_text_title_color,
                  marginLeft: 16,
                }}>
                {shop.Mobile.replace(/(\d{2})(\d{5})(\d{3})/, '$1*****$3')}
              </Text>
            </TouchableOpacity>
          )}

          {/* Email */}
          {shop?.Email && (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
                borderBottomWidth: 1,
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
              }}>
              <Winicon
                src="fill/user interface/mail"
                size={20}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
              <Text
                style={{
                  ...TypoSkin.regular2,
                  color: ColorThemes.light.neutral_text_title_color,
                  marginLeft: 16,
                }}>
                {shop.Email}
              </Text>
            </View>
          )}

          {/* Shop Info Header */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 12,
            }}>
            <Winicon
              src="fill/shopping/store"
              size={20}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
            <Text
              style={{
                ...TypoSkin.regular2,
                color: ColorThemes.light.neutral_text_title_color,
                marginLeft: 16,
              }}>
              Thông tin shop
            </Text>
          </View>
        </View>
        {/* Statistics Section */}
        <View
          style={{
            backgroundColor: 'white',
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <View
              style={{
                alignItems: 'center',
                flex: 1,
              }}>
              <Text
                style={{
                  ...TypoSkin.heading5,
                  color: ColorThemes.light.neutral_text_title_color,
                  marginBottom: 4,
                }}>
                {shop?.rating?.toFixed(1) ?? '4.5'}
              </Text>
              <Text
                style={{
                  ...TypoSkin.regular3,
                  color: ColorThemes.light.primary_main_color,
                }}>
                Đánh giá
              </Text>
            </View>

            <View
              style={{
                width: 1,
                height: 40,
                backgroundColor: ColorThemes.light.neutral_main_border_color,
              }}
            />

            <TouchableOpacity
              onPress={() => handleShopInfoPressLocal('product')}
              style={{
                alignItems: 'center',
                flex: 1,
              }}>
              <Text
                style={{
                  ...TypoSkin.heading5,
                  color: '#2196F3',
                  marginBottom: 4,
                }}>
                {shop?.totalProducts ?? '90'}
              </Text>
              <Text
                style={{
                  ...TypoSkin.regular3,
                  color: ColorThemes.light.primary_main_color,
                }}>
                Sản phẩm
              </Text>
            </TouchableOpacity>

            <View
              style={{
                width: 1,
                height: 40,
                backgroundColor: ColorThemes.light.neutral_main_border_color,
              }}
            />

            <View
              style={{
                alignItems: 'center',
                flex: 1,
              }}>
              <Text
                style={{
                  ...TypoSkin.heading5,
                  color: ColorThemes.light.neutral_text_title_color,
                  marginBottom: 4,
                }}>
                {shop?.totalOrder ?? '3000'}
              </Text>
              <Text
                style={{
                  ...TypoSkin.regular3,
                  color: ColorThemes.light.primary_main_color,
                }}>
                Đã bán
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
