import React, {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  Text,
  FlatList,
  ListRenderItem,
} from 'react-native';
import {Category} from '../../redux/models/category';
import FastImage from '@d11/react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import {ColorThemes} from '../../assets/skin/colors';
import {fetchCategories} from '../../redux/actions/categoryAction';
import {useDispatch, useSelector} from 'react-redux';
import {RootState} from '../../redux/store/store';
import {useCategoryHook} from '../../redux/hook/categoryHook';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../svg/icon';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../router/router';

const SCREEN_WIDTH = Dimensions.get('window').width;
const DRAWER_WIDTH = SCREEN_WIDTH * 0.55;
const EXPANDED_DRAWER_WIDTH = SCREEN_WIDTH * 1;
const ITEM_HEIGHT = 80;
const ITEM_WIDTH = 80;

interface DrawerProps {}

const DrawerCategories: React.FC<DrawerProps> = () => {
  const categoryHook = useCategoryHook();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const slideAnimation = useRef(new Animated.Value(-DRAWER_WIDTH)).current;
  const {data, showDrawer} = useSelector((state: RootState) => state.category);

  const [children, setChildren] = useState<Category[]>([]);
  const [chooseId, setChooseId] = useState<string>('');

  const onClose = useCallback(() => {
    categoryHook.setData('showDrawer', false);
  }, [categoryHook]);

  const handlePress = useCallback((item: Category) => {
    setChildren(item.Children);
    setChooseId(item.Id);
    Animated.timing(slideAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, []);

  const isChoose = useCallback(
    (id: string) => {
      return chooseId === id;
    },
    [chooseId],
  );

  useEffect(() => {
    if (showDrawer) {
      setChildren([]);
      setChooseId('');
      slideAnimation.setValue(-DRAWER_WIDTH);
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      Animated.timing(slideAnimation, {
        toValue: -DRAWER_WIDTH,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [showDrawer, slideAnimation]);

  useEffect(() => {
    dispatch(fetchCategories() as any);
  }, [dispatch]);

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    [],
  );

  const renderItem: ListRenderItem<Category> = useCallback(
    ({item}) => (
      <TouchableOpacity activeOpacity={0.7} onPress={() => handlePress(item)}>
        <View
          style={[
            styles.categoryContent,
            {borderWidth: isChoose(item.Id) ? 2 : 0.5},
          ]}>
          <View style={styles.iconContainer}>
            <FastImage
              key={item.Img}
              source={{uri: item.Img}}
              style={{width: 30, height: 30}}
              resizeMode={FastImage.resizeMode.contain}
            />
          </View>
          <Text style={styles.categoryName} numberOfLines={2}>
            {item.Name}
          </Text>
        </View>
      </TouchableOpacity>
    ),
    [handlePress, isChoose],
  );

  const keyExtractor = useCallback((item: Category) => item.Id, []);

  const navigateToProductList = useCallback(
    (categoryId: string) => {
      onClose();
      navigation.navigate(RootScreen.ProductListByCategory, {
        categoryId,
      });
    },
    [navigation, onClose],
  );

  const renderChildren = useMemo(() => {
    if (chooseId.length === 0) return null;

    return (
      <View style={{flexDirection: 'row', marginTop: 16}}>
        <View
          style={{
            marginHorizontal: 12,
            width: 1,
            backgroundColor: '#4EC6EA',
          }}
        />
        <View>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigateToProductList(chooseId)}>
            <Text style={styles.text}>Tất cả</Text>
          </TouchableOpacity>
          {children.map(item => (
            <TouchableOpacity
              key={item.Id}
              style={[styles.button, {marginTop: 12}]}
              onPress={() => navigateToProductList(item.Id)}>
              <Text style={styles.text}>{item.Name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  }, [children, chooseId]);

  if (!showDrawer) {
    return null;
  }

  return (
    <View style={styles.drawerRootContainer}>
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      />

      <Animated.View
        style={[
          styles.drawerContainer,
          {
            width: chooseId ? EXPANDED_DRAWER_WIDTH : DRAWER_WIDTH,
            transform: [{translateX: slideAnimation}],
          },
        ]}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
          }}>
          <Text style={styles.title}>Danh mục sản phẩm</Text>
          {chooseId.length > 0 && (
            <TouchableOpacity onPress={onClose}>
              <AppSvg SvgSrc={iconSvg.close} size={25} />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.contentContainer}>
          <View style={styles.categoriesContainer}>
            <FlatList
              data={data}
              renderItem={renderItem}
              keyExtractor={keyExtractor}
              numColumns={2}
              columnWrapperStyle={styles.columnWrapper}
              contentContainerStyle={styles.listContent}
              getItemLayout={getItemLayout}
              removeClippedSubviews={true}
              initialNumToRender={10}
              maxToRenderPerBatch={10}
              windowSize={5}
              key={'_'}
            />
          </View>
          {renderChildren}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  drawerRootContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  },
  overlay: {
    flex: 1,
  },
  drawerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    backgroundColor: 'white',
    paddingTop: 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  contentContainer: {
    flexDirection: 'row',
    marginLeft: 8,
  },
  categoriesContainer: {
    alignItems: 'center',
    paddingTop: 16,
  },
  columnWrapper: {
    gap: 12,
  },
  listContent: {
    gap: 12,
  },
  categoryContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#4EC6EA',
    height: ITEM_HEIGHT,
    width: ITEM_WIDTH,
    overflow: 'hidden',
    gap: 8,
  },
  iconContainer: {
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    lineHeight: 12,
    fontSize: 11,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#b0e0e6',
    borderRadius: 7,
    paddingVertical: 12,
    paddingHorizontal: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default DrawerCategories;
