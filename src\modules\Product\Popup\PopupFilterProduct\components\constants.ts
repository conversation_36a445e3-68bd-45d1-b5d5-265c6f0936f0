import {SelectableItem} from './types';

export const CATEGORIES: SelectableItem[] = [
  {id: 'cat_1', name: '<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> & <PERSON><PERSON><PERSON> bảng'},
  {id: 'cat_2', name: '<PERSON><PERSON> gia dụng'},
  {id: 'cat_3', name: 'Thời trang nam'},
];
export const BRANDS: SelectableItem[] = [
  {id: 'brand_1', name: 'Apple'},
  {id: 'brand_2', name: 'Samsung'},
  {id: 'brand_3', name: '<PERSON><PERSON>'},
  {id: 'brand_4', name: 'Sony'},
];
export const SORT_OPTIONS: SelectableItem[] = [
  {id: 'price_desc', name: '<PERSON><PERSON><PERSON> giả<PERSON> dần'},
  {id: 'price_asc', name: '<PERSON><PERSON><PERSON> tăng dần'},
  {id: 'newest', name: '<PERSON><PERSON><PERSON> nh<PERSON>'},
];
