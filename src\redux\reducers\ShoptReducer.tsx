import {
    Dispatch,
    PayloadAction,
    UnknownAction,
    createSlice,
} from '@reduxjs/toolkit';
import notifee from '@notifee/react-native';
import { DataController } from '../../base/baseController';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import store from '../store/store';
import { getDataToAsyncStorage, removeDataToAsyncStorage, saveDataToAsyncStorage } from '../../utils/AsyncStorage';
import { StorageContanst } from '../../Config/Contanst';
import { BaseDA } from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';
import ShopDA from '../../modules/shop/da';


interface ShopSimpleResponse {
    data?: any;
    onLoading?: boolean;
    type?: string;
}

const initState: ShopSimpleResponse = {
    data: undefined,
    onLoading: false,
};


export const shopSlice = createSlice({
    name: 'Shop',
    initialState: initState,
    reducers: {
        handleActionsShop: (state, action: PayloadAction<any>) => {
            switch (action.payload.type) {
                case 'GETINFORSHOP':
                    state.data = action.payload.data;
                    break;
                case 'UPDATE':
                    state.data = action.payload.data;
                    break;
                default:
                    break;
            }
            state.onLoading = false;
        },
        onFetching: state => {
            state.onLoading = true;
        },
    },
});

export const { handleActionsShop, onFetching } = shopSlice.actions;

export default shopSlice.reducer;


export class ProductActions {
    static getInforShop = (shopId: string) => async (dispatch: Dispatch) => {
        const shopDA = new ShopDA();
        const res = await shopDA.getShop(shopId)
        console.log("check-res-shop", res)
        if (res?.code === 200) {
            await saveDataToAsyncStorage(StorageContanst.ShopId, res.data[0].Id);
        }
        dispatch(
            handleActionsShop({
                type: 'GETINFORSHOP',
                data: res.data,
            }),
        );
    };

    static addShop = (shop: any) => async (dispatch: Dispatch) => {
        const shopController = new DataController('Shop');
        const res = await shopController.add(shop);
        console.log("check-res-addShop", res)
        if (res?.code === 200) {
            dispatch(handleActionsShop({ type: 'UPDATE', data: res.data }));
            return res;
        }
    };
    static editShop = (shop: any) => async (dispatch: Dispatch) => {
        const shopController = new DataController('Shop');
        const res = await shopController.edit(shop);
        console.log("check-res-editShop", res)
        if (res?.code === 200) {
            dispatch(handleActionsShop({ type: 'UPDATE', data: res.data }));
            return res;

        }
    };
}