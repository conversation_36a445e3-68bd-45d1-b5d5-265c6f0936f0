import {useState, useCallback} from 'react';
import {DataController} from '../../../../../base/baseController';

export const usePrice = (parentCategory?: string) => {
  const [price, setPrice] = useState(0);
  const [maxPrice, setMaxPrice] = useState(0);
  const [loading, setLoading] = useState(false);

  const getProductMaxPrice = useCallback(async () => {
    try {
      setLoading(true);
      const controller = new DataController('Product');
      const categoryController = new DataController('Category');

      const config: any = {
        page: 1,
        size: 1,
        sortby: [{prop: 'Price', direction: 'DESC'}],
      };

      // Nếu có parentCategory, filter sản phẩm theo CategoryId
      if (parentCategory) {
        const categories = await categoryController.aggregateList({
          searchRaw: `@ParentId:{${parentCategory}}`,
        });
        if (categories.code === 200) {
          config.searchRaw = `@CategoryId: {${categories.data
            .map((c: any) => c.Id)
            .join(' | ')}}`;
        }
      }

      const res = await controller.aggregateList(config);

      if (res.code === 200 && res.data.length > 0) {
        const maxPrice = res.data[0].Price ?? 0;
        setMaxPrice(maxPrice);
      }
    } catch (error) {
      console.error('Error fetching max price:', error);
    } finally {
      setLoading(false);
    }
  }, [parentCategory]);

  const resetPrice = useCallback(() => {
    setPrice(0);
  }, []);

  return {
    price,
    setPrice,
    maxPrice,
    loading,
    getProductMaxPrice,
    resetPrice,
  };
};
